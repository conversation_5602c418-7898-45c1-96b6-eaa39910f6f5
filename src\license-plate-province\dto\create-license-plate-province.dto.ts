import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateLicensePlateProvinceDto {

  @ApiProperty({
    example: 'P0001',
    description: 'The unique code of the  license plate province',
  })
  @IsString()
  code: string;

  @ApiProperty({
    example: 'กรุงเทพมหานคร',
    description: 'The name of the  license plate province',
  })
  @IsString()
  name: string;

  @ApiProperty({
    example: true,
    description: 'The status of the license plate province (e.g., active or inactive)',
  })
  @IsBoolean()
  is_status: boolean;

  @ApiProperty({
    example: true,
    description: 'Indicates whether the license plate province is active',
  })
  @IsBoolean()
  @IsOptional()
  is_active?: boolean;

  /* @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the  license plate province was created',
  })
  created_at: Date;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the  license plate province was last updated',
  })
  updated_at: Date; */
}
