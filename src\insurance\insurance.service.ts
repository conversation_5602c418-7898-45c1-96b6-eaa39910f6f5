import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { Insurance, Prisma } from '@prisma/client';
import { join } from 'path';
import { HandleErrorService } from "@common/services/handle-error.services";
import { getMockDataInsurance } from './mock/insurance.mock';

@Injectable()
export class InsuranceService {
  public static MODEL_NAME = 'insurance';
  constructor(private prisma: PrismaService,
    private readonly handleErrorService: HandleErrorService) { }

  async create(data: Prisma.CarBrandCreateInput): Promise<Insurance> {
    try {
      return await this.prisma.insurance.create({
        data: {
          ...data,
        },
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }

  }

  async findAll(filterText: string): Promise<Insurance[]> {
    return await this.prisma.insurance.findMany({
      /* where: filterText
        ? {
            name: {
              contains: filterText,
              // mode: 'insensitive',
            },
          }
        : {}, */
        where: {
          is_active: true,
          is_status: true,
          ...(filterText && {
            name: {
              contains: filterText,
              // mode: 'insensitive', // optional: case-insensitive match
            },
          }),
        },
    });
  }

  async findOne(id: number): Promise<Insurance> {
    return this.prisma.insurance.findUnique({ where: { id, is_active: true } });
  }

  async update(id: number, data: Prisma.InsuranceUpdateInput): Promise<Insurance> {
    try {
      return await this.prisma.insurance.update({
        where: { id },
        data
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }

  }

  async remove(id: number): Promise<Insurance> {
    const existData = await this.prisma.carCode.findUnique({ where: { id, is_active: true } });
    const date = Date.now();
    return this.prisma.insurance.update({
      where: { id },
      data: {
        code: date+'-delete-'+existData.code,
        is_active: false,
      },
    });
  }

  async generateCodeNumber() {
    const prefix = "CO";

    const lastRecord = await this.prisma.insurance.findFirst({
      where: {
        code: {
          startsWith: prefix,
        },
      },
      orderBy: {
        code: "desc",
      },
    });

    const lastNumber = lastRecord
      ? parseInt(lastRecord.code.replace(prefix, ""), 10)
      : 0;
    const newNumber = String(lastNumber + 1).padStart(3, "0");

    return `${prefix}${newNumber}`;
  }

  async getMockData(repairType: string, values?: number,firstPartDmgs?: number[], min?: number, max?: number, 
    addonServiceIds?: number[], companys?: number[], sortField?: string) {
    const data = await getMockDataInsurance(this.prisma)
    // let filtered = data;
    const firstPartDmgMap = {
      1: (item: any) => !item.is_excess || item.excess === 0,     // ไม่มี
      2: (item: any) => item.is_excess === true && item.excess === 3000,
      3: (item: any) => item.excess === 5000
    };

    const filtered = data.filter(item => {
      const matchesRepairType = !repairType || item.repair_type === repairType;
      const matchFirstPartDmg = !firstPartDmgs || firstPartDmgs.length === 0 || firstPartDmgs.some(id => firstPartDmgMap[id]?.(item));
      const matchesMin = min === undefined || item.current_insurance_value >= min;
      const matchesMax = max === undefined || item.current_insurance_value <= max;
      const matchAddOn = !addonServiceIds || addonServiceIds.length === 0 || item.insurance_addon_service?.some(addon => addonServiceIds.includes(addon.id));
      const itemIdFront = item.id.toString().split('-')[0];
      const matchCompany = !companys || companys.length === 0 || companys.includes(Number(itemIdFront));
      return matchesRepairType && matchFirstPartDmg && matchesMin && matchesMax && matchAddOn && matchCompany;
    });

    let field = sortField;
    let order: 'asc' | 'desc' = 'asc';

    if (sortField?.includes('_asc')) {
      field = sortField.replace('_asc', '');
      order = 'asc';
    } else if (sortField?.includes('_desc')) {
      field = sortField.replace('_desc', '');
      order = 'desc';
    }

    if (field) {
      filtered.sort((a, b) => {
        const aValue = a[field];
        const bValue = b[field];

        if (aValue === undefined || bValue === undefined) return 0;

        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return order === 'asc' ? aValue - bValue : bValue - aValue;
        }

        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return order === 'asc'
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        }

        return 0;
      });
    }

    return filtered;
  }
}
