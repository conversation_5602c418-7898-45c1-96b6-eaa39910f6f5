import {ConflictException } from '@nestjs/common';
import { Prisma } from '@prisma/client';

export class HandleErrorService {
    handlePrismaError(error: any): void {
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          if (error.code === 'P2002') {
            if (error.meta.target == 'user_username_key' ) {
              throw new ConflictException('USER_NAME_DUPLICATE');
            }else if (error.meta.target == 'user_email_key' ) {
              throw new ConflictException('EMAIL_DUPLICATE');
            }
            else {
              throw new ConflictException('CODE_DUPLICATE');
            }
          }
        }
        throw error; 
      }
  }