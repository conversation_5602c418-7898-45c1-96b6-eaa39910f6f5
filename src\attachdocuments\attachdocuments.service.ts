import { HandleErrorService } from "@common/services/handle-error.services";
import { Injectable } from "@nestjs/common";
import { Prisma } from "@prisma/client";
import path from "path";
import { HWC_UPLOAD_PATH } from "src/configs/server";
import { HwcFileUploadService } from "src/hwc-file-upload/hwc-file-upload.service";
import { PrismaService } from "src/prisma/prisma.service";
import { v4 as uuidv4 } from "uuid";
import { FileCustomers } from "./dto/updatecustomerfile";
import { CreatePolicyInformationDto } from "./dto/upload_attach_documents";
@Injectable()
export class AttachdocumentsService {
  constructor(
    private prisma: PrismaService,
    private readonly hwcFileUploadService: HwcFileUploadService,
    private readonly handleErrorService: HandleErrorService
  ) {}

  async update(
    id: number,
    filepath: FileCustomers[],
    data: CreatePolicyInformationDto
  ) {
    try {
      const save = await this.prisma.carInsuranceCustomer.update({
        where: { id: id },
        data: {
          car_insurance_id: data.car_insurance_id,
          registration_type_id: data.registration_type_id,
          prefix_name_id: data.prefix_name_id,
          license_plate_province_id: data.license_plate_province_id,
          business_type_id: data.business_type_id,
          phone_number: data.phone_number,
          first_name: data.first_name,
          last_name: data.last_name,
          business_name: data.bussiness_name,
          address: data.address,
          chassis_no: data.chassis_no,
          engine_no: data.engine_no,
          coverage_start_date: data.coverage_start_date,
          act_coverage_date: data.act_coverage_date,
          driver_type: data.driver_type,
          driver_name_1: data.driver_name_1,
          brith_date_driver_1: data.brith_date_driver_1,
          driver_name_2: data.driver_name_2,
          brith_date_driver_2: data.brith_date_driver_2,
          beneficiary_type: data.beneficiary_type,
          beneficiary_prefix_name_id: data.beneficiary_prefix_name_id,
          beneficiary_first_name: data.beneficiary_first_name,
          beneficiary_last_name: data.beneficiary_last_name,

          id_card_type: data.id_card_type,
          id_card_no: data.id_card_no,
          passport_no: data.passport_no,
          bussiness_name: data.bussiness_name,
          birth_date: data.birth_date,
          policy_house_no: data.policy_house_no,
          sub_district: data.sub_district,
          district: data.district,
          province: data.province,
          zip_code: data.zip_code,
          car_registration_number: data.car_registration_number,
          id_passport_file: data.id_passport_file,
          car_regis_book_file: data.car_regis_book_file,
          og_insur_policy_file: data.og_insur_policy_file,
          vehicle_fit_certi: data.vehicle_fit_certi,
          is_active: true,
          created_at: new Date(),
          agree: data.agree,
        },
      });

      if (save && filepath.length > 0) {
        const dataFile = filepath.map((file) => ({
          car_insurance_customer_id: save.id,
          image_path: file.filepath,
          carCustomerImageType: file.type,
        }));
        await this.prisma.carCustomerImage.createMany({
          data: dataFile as Prisma.CarCustomerImageCreateManyInput[],
        });
      }
      return save;
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }
  }

  async uploadFile(file: Express.Multer.File) {
    if (!file) {
      return null;
    }
    const extension = path.extname(file.originalname);
    const keyGenerate = `${HWC_UPLOAD_PATH}/${uuidv4()}${extension}`;
    try {
      await this.hwcFileUploadService.uploadFile({
        key: keyGenerate,
        file: file,
        success_action_status: "200",
      });
      return keyGenerate;
    } catch {
      return null;
    }
  }
}
