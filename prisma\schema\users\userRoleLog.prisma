model UserRoleLog {
    id             Int      @id @default(autoincrement())
    user_id        Int       
    user_role_id   Int       
    data           Json    
    is_status      <PERSON><PERSON><PERSON>  @default(true)
    is_active      <PERSON>olean  @default(true)
    created_at     DateTime @default(now())
    updated_at     DateTime @updatedAt

    // Relations
    admin    Admin      @relation(fields: [user_id], references: [user_id], onDelete: Cascade)
    userRole UserRole?   @relation(fields: [user_role_id], references: [id], onDelete: Cascade)

    @@map("user_role_log")
}
