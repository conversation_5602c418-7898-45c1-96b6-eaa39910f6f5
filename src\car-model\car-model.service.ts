import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
// import { CarModel } from './entities/car-model.entity';
import { CarModel } from '@prisma/client';
import { Prisma } from '@prisma/client';
import { join } from 'path';
import { CreateCarModelDto } from './dto/create-car-model.dto';
import { UpdateCarModelDto } from './dto/update-car-model.dto';
import { HandleErrorService } from "@common/services/handle-error.services";

@Injectable()
export class CarModelService {
  public static MODEL_NAME = 'carModel';
  constructor(private prisma: PrismaService,
    private readonly handleErrorService: HandleErrorService) { }

  async create(data: CreateCarModelDto): Promise<CarModel> {
    try {
      return await this.prisma.carModel.create({
        data
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }

  }

  async findAll(filters: { year?: string; car_brand_id?: string }): Promise<CarModel[]> {
    return await this.prisma.carModel.findMany({
      where: {
        is_active: true,
        ...(filters.year && { year: parseInt(filters.year, 10) }),
        ...(filters.car_brand_id && { car_brand_id: parseInt(filters.car_brand_id, 10) }),
      },
    });
  }

  async findOne(id: number): Promise<CarModel> {
    return this.prisma.carModel.findUnique({ where: { id, is_active: true }, include: { carBrand: true }, });
  }

  async update(id: number, data: UpdateCarModelDto): Promise<CarModel> {
    try {
      return await this.prisma.carModel.update({
        where: { id },
        data
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }

  }

  async remove(id: number): Promise<CarModel> {
    const existData = await this.prisma.carModel.findUnique({ where: { id, is_active: true } });
    const date = Date.now();
    return this.prisma.carModel.update({
      where: { id },
      data: {
        code: date+'-delete-'+existData.code,
        is_active: false,
      },
    });
  }
}
