import { PaginationService } from "@common/services/pagination.service";
import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UseGuards,
} from "@nestjs/common";
import {
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { User } from "src/_decorators/user-decorator/user-decorator.decorator";
import { JwtAuthGuard } from "src/auth/jwt-auth.guard";
import { CustomerService } from "./customer.service";
import { CreateCustomerDto } from "./dto/create-customer.dto";
import { Customer } from "./dto/entities/customer.entity";

@Controller("customer")
@ApiTags("customer")
export class CustomerController {
  constructor(
    private readonly customerService: CustomerService,
    private readonly paginationService: PaginationService
  ) {}
  @Get("paginate")
  @ApiOperation({ summary: "Get a list of all customers with pagination" })
  @ApiQuery({ name: "page", required: false, type: Number, example: 1 })
  @ApiQuery({ name: "perPage", required: false, type: Number, example: 10 })
  @ApiQuery({
    name: "searchText",
    required: false,
    type: String,
    example: "",
  })
  @ApiResponse({
    status: 200,
    description: "The list of customer has been successfully retrieved.",
    type: [Customer],
  })
  async paginationCustomer(
    @Query("page", ParseIntPipe) page: number = 1,
    @Query("perPage", ParseIntPipe) perPage: number = 10,
    @Query("searchText") searchText: string = ""
  ) {
    return this.paginationService.paginate(
      CustomerService.MODEL_NAME,
      page,
      perPage,
      {
        OR: [
          { first_name: { contains: searchText.toLowerCase() } },
          { last_name: { contains: searchText.toLowerCase() } },
          { phone_number: { contains: searchText.toLowerCase() } },
          { car_registration_number: { contains: searchText.toLowerCase() } },
        ],
      }
    );
  }
  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Create a new customer" })
  @ApiResponse({
    status: 201,
    description: "The customer has been successfully created.",
    type: CreateCustomerDto,
  })
  create(@Body() createCustomerDto: CreateCustomerDto, @User() user: any) {
    return this.customerService.create(user.id, createCustomerDto);
  }

  @Get(":id")
  @ApiResponse({
    status: 200,
    description: "get  customer by user_id",
  })
  findOne(@Param("id", ParseIntPipe) id: number) {
    return this.customerService.getCustomerByUserId(id);
  }
}
