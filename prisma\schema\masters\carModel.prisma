model CarModel {
    id           Int      @id @default(autoincrement())
    car_brand_id Int
    code         String   @unique @db.VarChar(50)
    name         String   @db.VarChar(255)
    year         Int    
    is_status    Boolean  @default(true)
    is_active    <PERSON>olean  @default(true)
    created_at   DateTime @default(now())
    updated_at   DateTime @updatedAt

    // Relations
    carBrand    CarBrand      @relation(fields: [car_brand_id], references: [id], onDelete: Cascade)
    carSubModel CarSubModel[]
    carInsurance CarInsurance[]
    carActPolicyDetail   CarActPolicyDetail[]
    @@map("car_model")
}
