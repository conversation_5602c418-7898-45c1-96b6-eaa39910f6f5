import { ApiProperty } from '@nestjs/swagger';

export class InsuranceAddonService {
  @ApiProperty({ example: 1 })
  id: number;

  @ApiProperty({
    example: 'I0001',
    description: 'The code of the InsuranceAddonService',
  })
  code: string;

  @ApiProperty({
    example: 'ช่วยเหลือฉุกเฉิน',
    description: 'The name of the InsuranceAddonService',
  })
  name: string;

  @ApiProperty({
    example: true,
    description: 'Indicates whether the InsuranceAddonService type is active',
  })
  is_active: boolean;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the InsuranceAddonService type was created',
  })
  created_at: Date;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the InsuranceAddonService type was last updated',
  })
  updated_at: Date;
}
