import { <PERSON>du<PERSON> } from "@nestjs/common";
import { UserController } from "./user.controller";
import { UserService } from "./user.service";
import { AdminService } from "../admin/admin.service";
import { PrismaModule } from "src/prisma/prisma.module";
import { FileService } from "@common/services/file.service";
import { PaginationService } from "@common/services/pagination.service";
import { HandleErrorService } from '@common/services/handle-error.services';
import { HashPasswordService } from '@common/services/hash-password.services';
import { ExcelService } from "@common/services/excel.service";

@Module({
  controllers: [UserController],
  providers: [UserService, FileService, PaginationService, HandleErrorService, AdminService, HashPasswordService, ExcelService],
  imports: [PrismaModule],
})
export class UserModule { }
