import { Module } from '@nestjs/common';
import { CarInsuranceCustomerController } from './car-insurance-customer.controller';
import { CarInsuranceCustomerService } from './car-insurance-customer.service';
import { PrismaModule } from 'src/prisma/prisma.module';
import { HandleErrorService } from '@common/services/handle-error.services';

@Module({
  controllers: [CarInsuranceCustomerController],
  providers: [CarInsuranceCustomerService, HandleErrorService],
  imports: [PrismaModule],
})
export class CarInsuranceCustomerModule {}
