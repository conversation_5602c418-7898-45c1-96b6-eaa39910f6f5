import { Body, Controller, HttpCode, Post, Req, Request } from '@nestjs/common';
import { AuthService } from './auth.service';
import { ApiOkResponse, ApiTags, ApiBody } from '@nestjs/swagger';
import { AuthEntity } from './entity/auth.entity';
import { LoginDto } from './dto/login.dto';
import { AuthDto } from './dto/auth.dto';

@Controller('auth')
@ApiTags('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  @HttpCode(200)
  @ApiOkResponse({ type: AuthEntity })
  login(@Body() { username, password }: LoginDto) {
    return this.authService.login(username, password);
  }

  @Post('refresh-token')
  @ApiBody({ type: AuthDto })
  @ApiOkResponse({ type: AuthEntity })
  refreshToken(@Req() request: Request, @Body() { token, client }: AuthDto) {
    // const clientMachine = request.headers['user-agent'];
    // console.log("🚀 ~ AuthController ~ refreshToken ~ clientMachine:", clientMachine)
    return this.authService.refreshToken(token, client);
  }
}
