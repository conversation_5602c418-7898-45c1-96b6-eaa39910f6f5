import { HandleErrorService } from "@common/services/handle-error.services";
import { ConflictException, Injectable } from "@nestjs/common";
import { PrismaService } from "src/prisma/prisma.service";
import { CreateCustomerDto } from "./dto/create-customer.dto";

@Injectable()
export class CustomerService {
  public static MODEL_NAME = "customer";
  constructor(
    private prisma: PrismaService,
    private readonly handleErrorService: HandleErrorService
  ) {}

  async create(user_id: number, createCustomerDto: CreateCustomerDto) {
    return this.prisma.$transaction(async (prisma) => {
      try {
        const { user_employee_id, ...rest } = createCustomerDto;

        // ตรวจสอบว่ามี record ซ้ำครบ 3 field หรือไม่
        const duplicateCustomer = await prisma.customer.findFirst({
          where: {
            user_id: user_id,
            car_registration_number: rest.car_registration_number,
            license_plate_province_id: rest.license_plate_province_id,
            registration_type_id: rest.registration_type_id,
          },
        });

        if (duplicateCustomer) {
          throw new ConflictException("CUSTOMER_ALREADY_EXISTS");
        }

        const customer = await prisma.customer.create({
          data: {
            ...rest,
            user_id: user_id,
          },
        });

        return customer;
      } catch (error) {
        this.handleErrorService.handlePrismaError(error);
      }
    });
  }
  async getCustomerByUserId(user_id: number) {
    return this.prisma.customer.findFirst({
      where: {
        user_id: user_id,
        is_active: true,
      },
    });
  }
  // async update
}
