model PrefixName {
    id   Int    @id @default(autoincrement())
    name String @db.VarChar(255)

    is_active  Boolean  @default(true)
    created_at DateTime @default(now())
    updated_at DateTime @updatedAt
    Admin      Admin[]
    carActPolicyPageDetail    CarActPolicyPageDetail[]
    carInsuranceCustomer      CarInsuranceCustomer[]
    customer                   Customer[]
    
    @@map("prefix_name")
}
