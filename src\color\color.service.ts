import { Injectable, ConflictException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { Prisma } from '@prisma/client';
import { Color } from './entities/color.entity';
import { HandleErrorService } from "@common/services/handle-error.services";
@Injectable()
export class ColorService {
  public static Model = 'color';
  constructor(private prisma: PrismaService,
    private readonly handleErrorService: HandleErrorService,
  ) { }

  async findAll(): Promise<Color[]> {

    return await this.prisma.color.findMany();
  }

  async findOne(id: number): Promise<Color> {
    return this.prisma.color.findUnique({ where: { id, is_active: true } });
  }

  async create(data: Prisma.ColorCreateInput) {
    try {
      return await this.prisma.color.create({
        data,
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }
  }


  async update(id: number, data: Prisma.ColorUpdateInput) {
    try {
      return await this.prisma.color.update({
        where: { id },
        data,
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }
  }

  async remove(id: number): Promise<Color> {
    const existData = await this.prisma.color.findUnique({ where: { id, is_active: true } });
    const date = Date.now();
    return this.prisma.color.update({
      where: { id },
      data: {
        code: date+'-delete-'+existData.code,
        is_active: false,
      },
    });
  }

  async generateCodeNumber() {
    const prefix = "CL";

    const lastRecord = await this.prisma.color.findFirst({
      where: {
        code: {
          startsWith: prefix,
        },
      },
      orderBy: {
        code: "desc",
      },
    });

    const lastNumber = lastRecord
      ? parseInt(lastRecord.code.replace(prefix, ""), 10)
      : 0;
    const newNumber = String(lastNumber + 1).padStart(3, "0");

    return `${prefix}${newNumber}`;
  }
}

