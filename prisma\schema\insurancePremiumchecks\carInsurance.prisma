model CarInsurance {
  id                        Int       @id @default(autoincrement())    
   user_id                   Int
  quotation_no              String?   @unique @db.VarChar(20)
  car_brand_id              Int       
  car_model_id              Int       
  car_submodel_id           Int?      
  insurance_type_id         Int       
  license_plate_province_id Int       
  car_code_id               Int       
  car_insurance_status_id   Int       
  year                      Int
  original_insurance_value  Decimal?  @db.Decimal(15, 2)
  current_insurance_value   Decimal   @db.Decimal(15, 2)
  reminder_status           String?    @db.VarChar(50)
  job_status                String?    @db.VarChar(50)
  is_active                 Boolean   @default(true)
  issue_date                DateTime?  
  expiry_date               DateTime?
  chassis_no                String?
  engine_no                 String?
  coverage_start_date       DateTime?
  act_coverage_date         DateTime?
   driver_type           EnumDriverType?
  // beneficiary_id            Int?
  color_id                  Int?

  created_at                DateTime  @default(now())
  updated_at                DateTime? @updatedAt

  // Relations
   user                      User                 @relation( fields: [user_id], references: [id])
  carBrand                  CarBrand             @relation(fields: [car_brand_id], references: [id])
  carModel                  CarModel             @relation(fields: [car_model_id], references: [id])
  carSubModel               CarSubModel?         @relation(fields: [car_submodel_id], references: [id])
  insuranceType             InsuranceType        @relation(fields: [insurance_type_id], references: [id])
  licensePlateProvince      LicensePlateProvince @relation(fields: [license_plate_province_id], references: [id])
  carCode                   CarCode              @relation(fields: [car_code_id], references: [id])
  carInsuranceStatus        CarInsuranceStatus   @relation(fields: [car_insurance_status_id], references: [id])
  color                     Color?               @relation(fields: [color_id], references: [id])

  carInsuranceAddons        CarInsuranceAddons[]
  carInsuranceCompare       CarInsuranceCompare[]
  carInsuranceCustomer      CarInsuranceCustomer[]
  carInsuranceDetail        CarInsuranceDetail[]
  carInsurancePayment       CarInsurancePayment[]
  @@map("car_insurance")
}

