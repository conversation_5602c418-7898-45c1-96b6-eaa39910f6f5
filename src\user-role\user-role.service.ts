import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
// import { UserRole } from './entities/car-model.entity';
import { UserRole } from '@prisma/client';
import { Prisma } from '@prisma/client';
import { join } from 'path';
import { CreateUserRoleDto } from './dto/create-user-role.dto';
import { UpdateUserRoleDto } from './dto/update-user-role.dto';
import { HandleErrorService } from "@common/services/handle-error.services";
import { UserRolePermissionService } from "../user-role-permission/user-role-permission.service";
import { UserRoleLogService } from "../user-role-log/user-role-log.service";


@Injectable()
export class UserRoleService {
  public static PATH_FILE = 'userRole';
  constructor(private prisma: PrismaService,
    private readonly handleErrorService: HandleErrorService,
    private  userRolePermissionService: UserRolePermissionService,
    private  userRoleLogService: UserRoleLogService) {}

    async create(data: CreateUserRoleDto): Promise<UserRole | null> {
      try {
        const { user_role_permission, ...userRoleData } = data;

        const CreatedUserRole = await this.prisma.$transaction(async (prisma) => {
          const role = await prisma.userRole.create({
            data: userRoleData,
          });

          if (role && user_role_permission && user_role_permission.length > 0) {
            await this.userRolePermissionService.create(prisma,user_role_permission, role.id);
          }

          if (role) {
             // TODO: Wait User Login
             let dataLog = {
              user_id: 1,
              user_role_id: role.id,
              data: data
            }
            await this.userRoleLogService.create(prisma,dataLog );
          }
          return role;
        });

        return CreatedUserRole;

      } catch (error) {
        this.handleErrorService.handlePrismaError(error);
      }
    }

    async update(id: number, data: UpdateUserRoleDto): Promise<UserRole | null> {
      try {
        const { user_role_permission, ...userRoleData } = data;
    
        const updatedUserRole = await this.prisma.$transaction(async (prisma) => {
          const role = await prisma.userRole.update({
            where: { id: id },
            data: userRoleData,
          });
    
          if (role && user_role_permission) {
            await this.userRolePermissionService.update(prisma, user_role_permission, role.id);
          }
          if (role) {
            // TODO: Wait User Login
            let dataLog = {
             user_id: 1,
             user_role_id: role.id,
             data: data
           }
           await this.userRoleLogService.create(prisma,dataLog );
         }

          return role;
        });
    
        return updatedUserRole;
      } catch (error) {
        this.handleErrorService.handlePrismaError(error);
      }
    }

  async findAll(): Promise<UserRole[]>  {
    return await this.prisma.userRole.findMany();
  }

  async findOne(id: number): Promise<UserRole> {
    return this.prisma.userRole.findUnique({ where: { id,is_active:true },include: { userRolePermission: true } });
  }


  

  async remove(id: number): Promise<UserRole> {
    return this.prisma.userRole.update({
      where: { id },
      data: {
        is_active: false,
      },
    });
  }
}
