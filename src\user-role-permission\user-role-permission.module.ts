import { Modu<PERSON> } from "@nestjs/common";
import { UserRolePermissionController } from "./user-role-permission.controller";
import { UserRolePermissionService } from "./user-role-permission.service";
import { PrismaModule } from "src/prisma/prisma.module";
import { FileService } from "@common/services/file.service";
import { PaginationService } from "@common/services/pagination.service";
import { HandleErrorService } from '@common/services/handle-error.services'; 

@Module({
  controllers: [UserRolePermissionController],
  providers: [UserRolePermissionService, FileService, PaginationService,HandleErrorService],
  imports: [PrismaModule],
})
export class UserRolePermissionModule {}
