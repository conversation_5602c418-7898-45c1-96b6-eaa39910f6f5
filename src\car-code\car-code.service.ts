import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { Prisma } from '@prisma/client';
import { CarCode } from './entities/car-code.entity';
import { HandleErrorService } from "@common/services/handle-error.services";
@Injectable()
export class CarCodeService {
  public static Model = 'carCode';
  constructor(private prisma: PrismaService,
    private readonly handleErrorService: HandleErrorService) { }

  async findAll(): Promise<CarCode[]> {
    return await this.prisma.carCode.findMany({
      where:{
        is_status: true,
        is_active: true
      }
    });
  }

  async findOne(id: number): Promise<CarCode> {
    return this.prisma.carCode.findUnique({ where: { id, is_active: true } });
  }

  async create(data: Prisma.CarCodeCreateInput): Promise<CarCode> {
    try {
      return await this.prisma.carCode.create({
        data,
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }
  }

  async update(id: number, data: Prisma.CarCodeUpdateInput) {
    try {
      return await this.prisma.carCode.update({
        where: { id },
        data,
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }

  }

  async remove(id: number): Promise<CarCode> {
    const existData = await this.prisma.carCode.findUnique({ where: { id, is_active: true } });
    const date = Date.now();
    return this.prisma.carCode.update({
      where: { id },
      data: {
        code: date+'-delete-'+existData.code,
        is_active: false,
      },
    });
  }
}

