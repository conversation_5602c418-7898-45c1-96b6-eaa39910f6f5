import { Controller, Get } from "@nestjs/common";
import { ApiOperation, ApiResponse } from "@nestjs/swagger";
import { PrefixNameService } from "./prefix-name.service";

@Controller("prefix-name")
export class PrefixNameController {
  constructor(
    private readonly prefixNameService: PrefixNameService
  ) {}

  @Get()
  @ApiOperation({ summary: "Get all mst access privilege " })
  //   @ApiQuery({ name: "page", required: false, type: Number, example: 1 })
  //   @ApiQuery({ name: "perPage", required: false, type: Number, example: 10 })
  //   @ApiQuery({ name: "s", required: false, type: String, example: "camry" })
  @ApiResponse({
    status: 200,
    description:
      "The list of mst access privilege has been successfully retrieved.",
  })
  findAll() {
    return this.prefixNameService.findAll();
  }
}
