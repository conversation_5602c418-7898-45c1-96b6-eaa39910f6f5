import { PartialType } from '@nestjs/swagger';
import { CreateYearDto } from './create-year.dto';
import { IsBoolean, IsNotEmpty, IsNumber, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateYearDto extends PartialType(CreateYearDto) {
  @ApiProperty({
    description: 'The name of the test.',
    example: 'Test',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    example: 'TOYOTA',
    description: 'The unique code of the  year',
  })
  code: string;

  @ApiProperty({
    description: 'Indicates whether the test is active.',
    example: true,
  })
  @IsBoolean()
  is_active: boolean;

  @ApiProperty({
    description: 'Indicates whether the test is status.',
    example: true,
  })
  @IsBoolean()
  is_status: boolean;
}
