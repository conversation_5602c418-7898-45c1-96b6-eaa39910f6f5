model InsuranceAddonService {
    id         Int      @id @default(autoincrement())
    code       String   @unique @db.VarChar(50)
    name       String   @db.VarChar(255)
    is_active  Boolean  @default(true)
    created_at DateTime @default(now())
    updated_at DateTime @updatedAt

    carInsuranceAddons         CarInsuranceAddons[]
    carInsuranceCompareAddons  CarInsuranceCompareAddons[]
    @@map("insurance_addon_service")
}
