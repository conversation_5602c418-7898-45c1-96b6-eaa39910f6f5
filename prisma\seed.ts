import { PrismaClient } from '@prisma/client';
import testData from './address/test.json';
import { placeSeeder } from './seeds/place';
import { mstAccessPrivilegeSeeder } from './seeds/mst-access-privilege';
import { perfixNameSeeder } from './seeds/prefix-name';
import { carInsuranceStatusSeeder } from './seeds/car-insurance-status';
import { insuranceAddonSeviceSeeder } from './seeds/insurance-addon-service';
import { businessTypeSeeder } from './seeds/business-type';
import { registrationTypeSeeder } from './seeds/registration-type';

const prisma = new PrismaClient();

async function main() {
  // await Promise.all(
  //   testData.map((n) =>
  //     prisma.testTable.upsert({
  //       where: { id: n.id },
  //       update: {
  //         name: n.name,
  //       },
  //       create: {
  //         name: n.name,
  //       },
  //     }),
  //   ),
  // );
  await Promise.all([
    placeSeeder(),
    mstAccessPrivilegeSeeder(),
    perfixNameSeeder(),
    carInsuranceStatusSeeder(),
    insuranceAddonSeviceSeeder(),
    businessTypeSeeder(),
    registrationTypeSeeder(),

  ]);

  console.log('Seed data created or updated successfully!');
}

// execute the main function
main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    // close the Prisma Client at the end
    await prisma.$disconnect();
  });
