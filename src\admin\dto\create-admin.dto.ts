import { IsBoolean, IsString, IsNotEmpty, IsArray, ValidateNested ,IsInt} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';


export class CreateAdminDto {
  @IsInt()
  @ApiProperty({ example: 1, required: true })
  user_id: number;
  
  @IsString()
  @ApiProperty({ type: 'string', format: 'binary', required: false })
  image_path?: string;
  
  @IsInt()
  @ApiProperty({ example: 1, required: false })
  prefix_name_id?: number;

  @IsString()
  @ApiProperty({ example: "เจตรินทร์", required: true })
  first_name: string;

  @IsString()
  @ApiProperty({ example: "สีแตง", required: false })
  last_name: string;

  @IsBoolean()
  @ApiProperty({ example: true, required: false ,type: Boolean  })
  is_status?: boolean = true;

  @IsBoolean()
  @ApiProperty({ example: true, required: false ,type: Boolean  })
  is_active?: boolean = true;
}