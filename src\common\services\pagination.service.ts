import { Injectable } from "@nestjs/common";
import { PrismaService } from "src/prisma/prisma.service";

@Injectable()
export class PaginationService {
    constructor(private prisma: PrismaService) {}
    async paginate<T>(
        model: any, // Replace with the appropriate model type from Prisma
        page: number = 1,
        perPage: number = 10,
        where: any = {}, // Add any filtering options
        include:any = {},
        orderBy: any = {},
      ): Promise<{
        data: any;
        meta: {
          total: number;
          lastPage: number;
          currentPage: number;
          perPage: number;
          prev: number | null;
          next: number | null;
        };
      }> {
        let prismaModel:any = this.prisma[model];
          where.is_active = true
        const [total, data] = await Promise.all([
          prismaModel.count({ where }),
          prismaModel.findMany({
            where,
            orderBy,
            include,
            skip: (page - 1) * perPage,
            take: perPage,
          }),
        ]);
    
        const lastPage = Math.ceil(total / perPage);
    
        return {
          data,
          meta: {
            total,
            lastPage,
            currentPage: page,
            perPage,
            prev: page > 1 ? page - 1 : null,
            next: page < lastPage ? page + 1 : null,
          },
        };
      }
}
