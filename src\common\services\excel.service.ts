import { Injectable } from '@nestjs/common';
import * as ExcelJS from 'exceljs';
import * as fs from 'fs';
import { Response } from 'express';

@Injectable()
export class ExcelService {
    async generateExcelFile(header: any[], data: any[], res: any): Promise<void> {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Sheet 1', {
            headerFooter: { firstHeader: "Hello Exceljs", firstFooter: "Hello World" }
        });

        // Add headers
        worksheet.columns = header;

        // Add data
        data.forEach((item) => {
            worksheet.addRow(item);
        });

        // Create buffer and send it as a download
        const buffer = await workbook.xlsx.writeBuffer();
        const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
        res.setHeader(
            'Content-Disposition',
            `attachment; filename=${uniqueSuffix}.xlsx`,
        );
        res.setHeader(
            'Content-Type',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        );
        res.send(buffer);
    }
}