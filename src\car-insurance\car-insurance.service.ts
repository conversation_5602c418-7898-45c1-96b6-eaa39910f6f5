import { Injectable } from "@nestjs/common";
import { PrismaService } from "src/prisma/prisma.service";
// import { CarInsurance } from './entities/car-model.entity';
import { HandleErrorService } from "@common/services/handle-error.services";
import { CarInsurance } from "@prisma/client";
import { CreateCarInsuranceDto } from "./dto/create-car-insurance.dto";
import { UpdateCarInsuranceDto } from "./dto/update-car-insurance.dto";

@Injectable()
export class CarInsuranceService {
  public static MODEL_NAME = "carInsurance";
  constructor(
    private prisma: PrismaService,
    private readonly handleErrorService: HandleErrorService
  ) {}

  async create(
    data: CreateCarInsuranceDto,
    user_id: number
  ): Promise<CarInsurance> {
    try {
      const quotation_no = await this.getNextQuotationNo();
      return await this.prisma.carInsurance.create({
        data: {
          ...(data as any),
          quotation_no,
          user_id: user_id,
        },
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }
  }

  async findAll(filters: { car_model_id?: string }): Promise<CarInsurance[]> {
    return await this.prisma.carInsurance.findMany({
      where: {
        is_active: true,
        ...(filters.car_model_id && {
          car_model_id: parseInt(filters.car_model_id, 10),
        }),
      },
    });
  }

  async findOne(id: number): Promise<CarInsurance> {
    return this.prisma.carInsurance.findUnique({
      where: { id, is_active: true },
      include: {
        user: true,
        carModel: true,
        carBrand: true,
        carSubModel: true,
        insuranceType: {
          include: {
            place: true,
          },
        },
        licensePlateProvince: true,
        carCode: true,
        carInsuranceStatus: true,
        carInsuranceCustomer: {
          include: {
            licensePlateProvince: true,
          },
        },
        carInsuranceCompare: {
          include: {
            insurance: true,
            insuranceType: {
              include: {
                place: true,
              },
            },
            carInsuranceCompareAddons: {
              include: {
                insuranceAddonService: true,
              },
            },
          },
        },
      },
    });
  }

  async update(
    id: number,
    updateCarInsuranceDto: UpdateCarInsuranceDto,
    user_id: number
  ) {
    try {
      return await this.prisma.$transaction(async (prisma) => {
        // แยก car_insurance_customer ออกมา ไม่ให้เข้าไปใน Prisma โดยตรง
        const {
          car_insurance_customer,
          car_insurance_compares,
          ...carInsuranceData
        } = updateCarInsuranceDto;

        // 1. อัปเดตข้อมูลหลักของ CarInsurance
        const updatedCarInsurance = await prisma.carInsurance.update({
          where: { id },
          data: carInsuranceData,
        });

        // 2. ถ้ามี customer เข้ามา สร้างใหม่
        if (car_insurance_customer) {
          // ลบข้อมูล customer เดิม
          await prisma.carInsuranceCustomer.deleteMany({
            where: { car_insurance_id: id },
          });

          // สร้าง customer ใหม่
          await prisma.carInsuranceCustomer.create({
            data: {
              ...(car_insurance_customer as any),
              car_insurance_id: id,
            },
          });
        }

        if (
          Array.isArray(car_insurance_compares) &&
          car_insurance_compares.length > 0
        ) {
          await prisma.carInsuranceCompare.deleteMany({
            where: { car_insurance_id: id },
          });

          for (const compare of car_insurance_compares) {
            const { car_insurance_compare_addons, ...compareData } = compare;

            const createdCompare = await prisma.carInsuranceCompare.create({
              data: {
                ...compareData,
                car_insurance_id: id,
              },
            });

            if (
              Array.isArray(compare.car_insurance_compare_addons) &&
              compare.car_insurance_compare_addons.length > 0
            ) {
              const addons = compare.car_insurance_compare_addons.map(
                (addon) => ({
                  car_insurance_compare_id: createdCompare.id,
                  insurance_addon_service_id: addon.insurance_addon_service_id,
                })
              );
              await prisma.carInsuranceCompareAddons.createMany({
                data: addons,
              });
            }
          }
        }
        return updatedCarInsurance;
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }
  }

  async remove(id: number): Promise<CarInsurance> {
    return this.prisma.carInsurance.update({
      where: { id },
      data: {
        is_active: false,
      },
    });
  }

  async getNextQuotationNo(): Promise<string> {
    const lastQuotation = await this.prisma.carInsurance.findFirst({
      orderBy: {
        quotation_no: "desc",
      },
      where: {
        quotation_no: {
          not: null,
        },
      },
    });

    if (lastQuotation?.quotation_no) {
      const lastNumber = parseInt(lastQuotation.quotation_no, 10);
      const nextNumber = lastNumber + 1;
      return nextNumber.toString().padStart(6, "0");
    }

    return "000001";
  }
}
