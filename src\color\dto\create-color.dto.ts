import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateColorDto {
  @ApiProperty({
    description: 'The name of the test.',
    example: 'Test',
  })
  @IsString()
  @IsNotEmpty()
  name: string;
  
  @ApiProperty({
    example: 'CL001',
    description: 'The unique code of the  color',
  })
  @IsString()
  code: string;

  @ApiProperty({
    description: 'Indicates whether the test is status.',
    example: true,
  })
  @IsBoolean()
  is_status: boolean;

  @ApiProperty({
    description: 'Indicates whether the test is active.',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  is_active?: boolean;
}


