model Permission {     
    // old column
    // id                 Int                  @id @default(autoincrement())
    // parent_id          Int?                 @default(0)
    // code               String               @unique
    // name               String
    // is_active          <PERSON><PERSON>an              @default(true)
    // created_at         DateTime             @default(now())
    // updated_at         DateTime             @updatedAt

    // new column
    id                  Int                  @id @default(autoincrement())
    lv_code             String               @unique
    name                String
    is_status           Boolean              @default(true)
    is_active           <PERSON><PERSON>an              @default(true)
    user_emp_update_id  Int?
    created_at          DateTime             @default(now())
    updated_at          DateTime             @updatedAt
    userRolePermission UserRolePermission[]
    permissionAccessPrivilege     PermissionAccessPrivilege[]
    @@map("permission")
}
