import { HandleErrorService } from "@common/services/handle-error.services";
import { Injectable } from "@nestjs/common";
import { PrismaService } from "src/prisma/prisma.service";
import { AdminService } from "../admin/admin.service";
import { CreateAdminDto } from "../admin/dto/create-admin.dto";
import { UpdateAdminDto } from "../admin/dto/update-admin.dto";
import { CreateUserDto } from "./dto/create-user.dto";
import { UpdateUserDto } from "./dto/update-user.dto";
import { User } from "./entities/user.entity";
@Injectable()
export class UserService {
  public static MODEL_NAME = "user";
  constructor(
    private prisma: PrismaService,
    private readonly adminService: AdminService,
    private readonly handleErrorService: HandleErrorService
  ) {}

  async createUserAndAdmin(
    createUserDto: CreateUserDto,
    adminDto: CreateAdminDto,
    image_path: Express.Multer.File
  ) {
    return this.prisma.$transaction(async (prisma) => {
      const createdUser = await prisma.user.create({
        data: {
          ...createUserDto,
        },
      });

      const createdAdmin = await this.adminService.create(
        prisma,
        {
          user_id: createdUser.id,
          prefix_name_id: adminDto.prefix_name_id,
          first_name: adminDto.first_name,
          last_name: adminDto.last_name,
          is_active: adminDto.is_active,
        },
        image_path
      );

      return { user: createdUser, admin: createdAdmin };
    });
  }
  async updateUserAndAdmin(
    userId: number,
    updateUserDto: UpdateUserDto,
    updateAdminDto: UpdateAdminDto,
    image_path: Express.Multer.File
  ) {
    return this.prisma.$transaction(async (prisma) => {
      // อัปเดตข้อมูลในตาราง User
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          ...updateUserDto,
        },
      });

      // อัปเดตข้อมูลในตาราง Admin
      const updatedAdmin = await this.adminService.update(
        prisma,
        updatedUser.id,
        updateAdminDto,
        image_path
      );

      return { user: updatedUser, admin: updatedAdmin };
    });
  }
  async create(data: CreateUserDto): Promise<User> {
    try {
      return await this.prisma.user.create({
        data: data,
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }
  }

  async findAll(): Promise<User[]> {
    const users = await this.prisma.user.findMany({
      include: {
        admin: {
          where: {
            is_active: true, // Filter for active admins
          },
        },
        userRole: true,
      },
    });
    return users.map((user) => {
      return {
        ...user,
        admin: user.admin[0], // Replace 'customAdminKey' with your desired key
      };
    });
  }

  async findOne(id: number): Promise<User> {
    return this.prisma.user.findUnique({
      where: { id, is_active: true },
      include: { admin: true, agent: true },
    });
  }

  async remove(id: number): Promise<User> {
    return this.prisma.user.update({
      where: { id },
      data: {
        is_active: false,
      },
    });
  }
}
