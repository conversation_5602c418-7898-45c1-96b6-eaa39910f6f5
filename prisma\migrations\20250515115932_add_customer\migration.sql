-- CreateTable
CREATE TABLE `customer` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_employee_id` INTEGER NOT NULL,
    `registration_type_id` INTEGER NOT NULL,
    `prefix_name_id` INTEGER NULL,
    `license_plate_province_id` INTEGER NOT NULL,
    `business_type_id` INTEGER NOT NULL,
    `phone_number` VARCHAR(20) NULL,
    `first_name` VARCHAR(191) NULL,
    `last_name` VARCHAR(191) NULL,
    `business_name` VARCHAR(191) NULL,
    `address` TEXT NULL,
    `car_registration_number` VARCHAR(50) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Add<PERSON><PERSON><PERSON>Key
ALTER TABLE `customer` ADD CONSTRAINT `customer_user_employee_id_fkey` FOREIGN KEY (`user_employee_id`) REFERENCES `user_employee`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `customer` ADD CONSTRAINT `customer_registration_type_id_fkey` FOREIGN KEY (`registration_type_id`) REFERENCES `registration_type`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `customer` ADD CONSTRAINT `customer_prefix_name_id_fkey` FOREIGN KEY (`prefix_name_id`) REFERENCES `prefix_name`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `customer` ADD CONSTRAINT `customer_license_plate_province_id_fkey` FOREIGN KEY (`license_plate_province_id`) REFERENCES `license_plate_province`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `customer` ADD CONSTRAINT `customer_business_type_id_fkey` FOREIGN KEY (`business_type_id`) REFERENCES `business_type`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
