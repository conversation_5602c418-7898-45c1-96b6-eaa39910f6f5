/*
  Warnings:

  - You are about to drop the column `province_id` on the `car_insurance_customer` table. All the data in the column will be lost.
  - Added the required column `license_plate_province_id` to the `car_insurance_customer` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `car_insurance_customer` DROP FOREIGN KEY `car_insurance_customer_province_id_fkey`;

-- AlterTable
ALTER TABLE `car_insurance_customer` DROP COLUMN `province_id`,
    ADD COLUMN `license_plate_province_id` INTEGER NOT NULL;

-- AddForeignKey
ALTER TABLE `car_insurance_customer` ADD CONSTRAINT `car_insurance_customer_license_plate_province_id_fkey` FOREIGN KEY (`license_plate_province_id`) REFERENCES `license_plate_province`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
