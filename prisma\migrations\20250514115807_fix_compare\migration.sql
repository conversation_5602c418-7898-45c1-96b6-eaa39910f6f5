-- CreateTable
CREATE TABLE `car_insurance_compare` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `car_insurance_id` INTEGER NOT NULL,
    `insurance_id` INTEGER NOT NULL,
    `insurance_type_id` INTEGER NOT NULL,
    `is_named_driver` BOOLEAN NOT NULL DEFAULT true,
    `is_excess` BOOLEAN NOT NULL DEFAULT true,
    `price` DECIMAL(15, 2) NULL,
    `prb_price` DECIMAL(15, 2) NULL,
    `vehicle_damage` DECIMAL(15, 2) NULL,
    `vehicle_loss_or_fire` DECIMAL(15, 2) NULL,
    `external_person_life_per_person` DECIMAL(15, 2) NULL,
    `external_person_life_per_case` DECIMAL(15, 2) NULL,
    `external_person_property` DECIMAL(15, 2) NULL,
    `excess_property_damage` DECIMAL(15, 2) NULL,
    `personal_accident` DECIMAL(15, 2) NULL,
    `medical_expense_per_person` DECIMAL(15, 2) NULL,
    `driver_bail` DECIMAL(15, 2) NULL,
    `current_insurance_value` DECIMAL(15, 2) NULL,
    `marketing_cost` DECIMAL(15, 2) NULL,
    `excess` DECIMAL(15, 2) NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `car_insurance_compare_addons` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `car_insurance_compare_id` INTEGER NOT NULL,
    `insurance_addon_service_id` INTEGER NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `car_insurance_compare` ADD CONSTRAINT `car_insurance_compare_car_insurance_id_fkey` FOREIGN KEY (`car_insurance_id`) REFERENCES `car_insurance`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_compare` ADD CONSTRAINT `car_insurance_compare_insurance_id_fkey` FOREIGN KEY (`insurance_id`) REFERENCES `insurance`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_compare` ADD CONSTRAINT `car_insurance_compare_insurance_type_id_fkey` FOREIGN KEY (`insurance_type_id`) REFERENCES `insurance_type`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_compare_addons` ADD CONSTRAINT `car_insurance_compare_addons_car_insurance_compare_id_fkey` FOREIGN KEY (`car_insurance_compare_id`) REFERENCES `car_insurance_compare`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_compare_addons` ADD CONSTRAINT `car_insurance_compare_addons_insurance_addon_service_id_fkey` FOREIGN KEY (`insurance_addon_service_id`) REFERENCES `insurance_addon_service`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
