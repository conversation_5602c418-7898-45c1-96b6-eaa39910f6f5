import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEmail, IsInt, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateUserDto {
  // User properties
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'john_doe', description: 'The unique username of the user' })
  username: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'securePassword123', description: 'The password for the user account' })
  password: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ example: '+**********', description: 'The phone number of the user', required: false })
  phone_number?: string;

  @IsEmail()
  @IsNotEmpty()
  @ApiProperty({ example: '<EMAIL>', description: 'The email address of the user' })
  email: string;

  @IsBoolean()
  @ApiProperty({ example: true, description: 'Indicates whether the user is active', type: <PERSON>ole<PERSON> })
  is_active: boolean;

  @IsInt()
  @ApiProperty({ example: 1, description: 'The unique user_role_id of the user', type: Number })
  user_role_id: number;

  @IsInt()
  @ApiProperty({ example: 1, description: 'The unique user_type_id of the user', type: Number })
  user_type_id: number;

 

  @IsOptional()
  @IsInt()
  @ApiProperty({ example: 3, description: 'The parent user ID', required: false })
  parent_id?: number;

}