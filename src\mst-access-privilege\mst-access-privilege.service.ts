import { Injectable } from '@nestjs/common';
import { MstAccessPrivilege } from '@prisma/client';
import { PrismaService } from 'src/prisma/prisma.service';

@Injectable()
export class MstAccessPrivilegeService {
    public static MODEL_NAME = 'mstAccessPrivilege';
    constructor(private prisma: PrismaService) { }

    async findAll(): Promise<MstAccessPrivilege[]> {
        return await this.prisma.mstAccessPrivilege.findMany();
    }
}
