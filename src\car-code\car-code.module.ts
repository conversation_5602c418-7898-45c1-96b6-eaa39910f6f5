import { Modu<PERSON> } from '@nestjs/common';
import { CarCodeController } from './car-code.controller';
import { CarCodeService } from './car-code.service';
import { PrismaModule } from 'src/prisma/prisma.module';
import { PaginationService } from "@common/services/pagination.service";
import { HandleErrorService } from '@common/services/handle-error.services'; 

@Module({
  controllers: [CarCodeController],
  providers: [CarCodeService,PaginationService,HandleErrorService],
  imports: [PrismaModule],
})
export class CarCodeModule {}
