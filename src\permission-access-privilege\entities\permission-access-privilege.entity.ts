import { ApiProperty } from '@nestjs/swagger';

export class PermissionAccessPrivilege {
  @ApiProperty({
    example: 1,
    description: 'The unique identifier of the permission access privilege',
  })
  id: number;

  @ApiProperty({
    example: 5,
    description: 'The permission ID of the permission access privilege, if any',
    type: Number,
  })
  permission_id?: number;

  @ApiProperty({
    example: 10,
    description: 'The mst access privilege ID of the permission access privilege, if any',
    type: Number,
  })
  mst_access_privilege_id?: number;

  @ApiProperty({
    example: true,
    description: 'Indicates whether the access privilege is allow or not',
  })
  is_allow: boolean;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the permission access privilege was created',
  })
  created_at: Date;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the permission access privilege was last updated',
  })
  updated_at: Date;
}