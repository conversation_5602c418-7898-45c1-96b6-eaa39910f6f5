import { Module } from '@nestjs/common';
import { CustomerController } from './customer.controller';
import { CustomerService } from './customer.service';
import { PaginationService } from '@common/services/pagination.service';
import { HandleErrorService } from '@common/services/handle-error.services';
import { PrismaModule } from 'src/prisma/prisma.module';

@Module({
  controllers: [CustomerController],
  providers: [CustomerService,PaginationService, HandleErrorService],
  imports: [PrismaModule]
})
export class CustomerModule {}
