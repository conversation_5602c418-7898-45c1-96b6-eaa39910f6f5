import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import * as bcrypt from "bcrypt";
import { PrismaService } from "./../prisma/prisma.service";
import { AuthEntity } from "./entity/auth.entity";

@Injectable()
export class AuthService {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService
  ) {}

  async login(username: string, inputPassword: string): Promise<AuthEntity> {
    const user = await this.prisma.user.findUnique({
      where: { username: username },
    });

    if (!user) {
      throw new NotFoundException(`No user found for account: ${user}`);
    }

    const compatibleHash = user.password.replace(/^\$2y\$/, "$2a$");
    const isPasswordValid = await bcrypt.compare(inputPassword, compatibleHash);

    /* if (!isPasswordValid) {
      throw new UnprocessableEntityException('Invalid password');
    } */

    const { password, ...userWithoutPassword } = user;
    return {
      accessToken: this.jwtService.sign({
        user: userWithoutPassword,
      }),
    };
  }

  async refreshToken(
    token: string,
    clientMachine?: string
  ): Promise<AuthEntity> {
    try {
      const decoded = await this.jwtService.decode(token);

      const user = await this.prisma.user.findFirst({
        where: { id: decoded.user.id },
      });

      const { password, ...userWithoutPassword } = user;
      return {
        accessToken: this.jwtService.sign({
          user: userWithoutPassword,
        }),
      };
    } catch (err) {
      console.log(
        "🚀 ~ file: auth.service.ts:100 ~ AuthService ~ refreshToken ~ err:",
        err
      );
      throw new InternalServerErrorException(
        `Something went wrong please try again later.`
      );
    }
  }
}
