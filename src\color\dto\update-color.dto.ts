import { PartialType } from '@nestjs/swagger';
import { CreateColorDto } from './create-color.dto';
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateColorDto extends PartialType(CreateColorDto) {
  @ApiProperty({
    description: 'The name of the test.',
    example: 'Test',
  })
  @IsString()
  @IsNotEmpty()
  name: string;
  
  @ApiProperty({
    example: 'CL001',
    description: 'The unique code of the  color',
  })
  @IsString()
  code: string;

  @ApiProperty({
    description: 'Indicates whether the test is status.',
    example: true,
  })
  @IsBoolean()
  is_status: boolean;
  
  @ApiProperty({
    description: 'Indicates whether the test is active.',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  is_active?: boolean;
}
