import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  UploadedFile,
  UseInterceptors,
  ParseIntPipe,
  Query,
  UploadedFiles,
  UseGuards,
} from "@nestjs/common";
import { CarBrandService } from "./car-brand.service";
import { CarBrand } from "./entities/car-brand.entity";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { FileFieldsInterceptor, FileInterceptor, FilesInterceptor } from "@nestjs/platform-express";
import { diskStorage } from "multer";
import { CreateCarBrandDto } from "./dto/create-car-brand.dto";
import { UpdateCarBrandDto } from "./dto/update-car-brand.dto";
import path, { join } from "path";
import { FileService } from "@common/services/file.service"; // Assuming this is where your utility function is
import { ConvertService } from "@common/services/convert.service";
import { PaginationService } from "@common/services/pagination.service";
import { HwcFileUploadService } from "src/hwc-file-upload/hwc-file-upload.service";
import { HWC_UPLOAD_PATH } from "src/configs/server";
import { v4 as uuidv4 } from 'uuid'
import { JwtAuthGuard } from "src/auth/jwt-auth.guard";

@ApiTags("car-brand")
@Controller("car-brand")
export class CarBrandController {
  constructor(
    private readonly carBrandService: CarBrandService,
    private readonly paginationService: PaginationService,
    private fileService: FileService,
    private readonly hwcFileUploadService: HwcFileUploadService,
  ) { }

  @Post()
  @ApiOperation({ summary: "Create a new car brand" })
  @ApiConsumes("multipart/form-data")
  @ApiResponse({
    status: 201,
    description: "The car brand has been successfully created.",
    type: CreateCarBrandDto,
  })
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'image_file', maxCount: 1 },
    ]),
    FilesInterceptor,
  )
  async create(
    @UploadedFiles()
    files: {
      image_file?: Express.Multer.File[];
    },
    @Body() createCarBrandDto: CreateCarBrandDto
  ) {
    const imageFile = files?.image_file?.[0];

    if (imageFile) {
      const extension = path.extname(imageFile.originalname);
      const keyGenerate = `${HWC_UPLOAD_PATH}/${uuidv4()}${extension}`;
      try {
        await this.hwcFileUploadService.uploadFile({
            key: keyGenerate,
            file: imageFile,
            success_action_status: '200',
        });
      } catch (error) {
        console.error('File upload failed:', error.message);
        throw error;
      }
      createCarBrandDto.image_path = keyGenerate;
    }

    return this.carBrandService.create(createCarBrandDto);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update a car brand by ID" })
  @ApiConsumes("multipart/form-data")
  @ApiResponse({
    status: 200,
    description: "The car brand has been successfully updated.",
    type: UpdateCarBrandDto,
  })
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'image_file', maxCount: 1 },
    ]),
    FilesInterceptor,
  )
  async update(
    @Param("id", ParseIntPipe) id: number,
    @UploadedFiles()
    files: {
      image_file?: Express.Multer.File[];
    },
    @Body() updateCarBrandDto: UpdateCarBrandDto
  ) {
    // Convert string fields to boolean
    /* updateCarBrandDto.is_status = ConvertService.stringToBoolean(
      updateCarBrandDto.is_status
    );
    updateCarBrandDto.is_active = ConvertService.stringToBoolean(
      updateCarBrandDto.is_active
    ); */
    const imageFile = files?.image_file?.[0];

    const existingCarBrand = await this.carBrandService.findOne(id);

    if (imageFile) {
      const extension = path.extname(imageFile.originalname);
      const keyGenerate = `${HWC_UPLOAD_PATH}/${uuidv4()}${extension}`;
      try {
        await this.hwcFileUploadService.uploadFile({
            key: keyGenerate,
            file: imageFile,
            success_action_status: '200',
        });
      } catch (error) {
        console.error('File upload failed:', error.message);
        throw error;
      }
      updateCarBrandDto.image_path = keyGenerate;
    }

    return this.carBrandService.update(id, updateCarBrandDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get insurance type model All" })
  @ApiResponse({
    status: 200,
    description: "The insurance type has been successfully retrieved.",
    type: CarBrand,
  })
  findAll() {
    return this.carBrandService.findAll();
  }

  @Get("paginate")
  @ApiOperation({ summary: "Get a list of all car brands with pagination" })
  @ApiQuery({ name: "page", required: false, type: Number, example: 1 })
  @ApiQuery({ name: "perPage", required: false, type: Number, example: 10 })
  @ApiQuery({ name: "searchText", required: false, type: String, example: "toyota" })
  @ApiResponse({
    status: 200,
    description: "The list of car brands has been successfully retrieved.",
    type: [CarBrand],
  })
  async pagination(
    @Query("page", ParseIntPipe) page: number = 1,
    @Query("perPage", ParseIntPipe) perPage: number = 10,
    @Query("searchText") searchText: string = ""
  ) {
    return this.paginationService.paginate(
      CarBrandService.MODEL_NAME,
      page,
      perPage,
      {
        OR: [
          {name: { contains: searchText.toLowerCase() }},
          {code: { contains: searchText.toLowerCase() }}
        ]
      }
    );
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a single car brand by ID" })
  @ApiResponse({
    status: 200,
    description: "The car brand has been successfully retrieved.",
    type: CarBrand,
  })
  findOne(@Param("id", ParseIntPipe) id: number) {
    return this.carBrandService.findOne(id);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a car brand by ID" })
  @ApiResponse({
    status: 200,
    description: "The car brand has been successfully deleted.",
    type: CarBrand,
  })
  remove(@Param("id", ParseIntPipe) id: number) {
    return this.carBrandService.remove(id);
  }
}
