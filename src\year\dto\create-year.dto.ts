import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsString } from 'class-validator';

export class CreateYearDto {
  @ApiProperty({
    description: 'The name of the test.',
    example: 'Test',
  })
  @IsString()
  @IsNotEmpty()
  name: string;
  
  @ApiProperty({
    example: 'TOYOTA',
    description: 'The unique code of the  year',
  })
  code: string;

  @ApiProperty({
    description: 'Indicates whether the test is active.',
    example: true,
  })
  @IsBoolean()
  is_active: boolean;

  @ApiProperty({
    description: 'Indicates whether the test is status.',
    example: true,
  })
  @IsBoolean()
  is_status: boolean;
}


