-- CreateTable
CREATE TABLE `car_model` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `car_brand_id` INTEGER NOT NULL,
    `code` VARCHAR(50) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `year` VARCHAR(255) NOT NULL,
    `is_status` BOOLEAN NOT NULL DEFAULT true,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `car_model_code_key`(`code`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `car_model` ADD CONSTRAINT `car_model_car_brand_id_fkey` FOREIGN KEY (`car_brand_id`) REFERENCES `car_brand`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
