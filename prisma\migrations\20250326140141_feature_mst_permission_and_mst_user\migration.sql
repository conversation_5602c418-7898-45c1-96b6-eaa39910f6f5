/*
  Warnings:

  - You are about to drop the column `code` on the `permission` table. All the data in the column will be lost.
  - You are about to drop the column `parent_id` on the `permission` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[lv_code]` on the table `permission` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `lv_code` to the `permission` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX `permission_code_key` ON `permission`;

-- AlterTable
ALTER TABLE `permission` DROP COLUMN `code`,
    DROP COLUMN `parent_id`,
    ADD COLUMN `is_status` BOOLEAN NOT NULL DEFAULT true,
    ADD COLUMN `lv_code` VARCHAR(191) NOT NULL,
    ADD COLUMN `user_emp_update_id` INTEGER NULL;

-- CreateTable
CREATE TABLE `mst_access_privilege` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `is_status` BOOLEAN NOT NULL DEFAULT true,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `permission_access_privilege` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `permission_id` INTEGER NOT NULL,
    `mst_access_privilege_id` INTEGER NOT NULL,
    `is_allow` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user_employee` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `prefix_name_id` INTEGER NOT NULL,
    `permission_id` INTEGER NOT NULL,
    `user_code` VARCHAR(20) NOT NULL,
    `image_path` VARCHAR(191) NULL,
    `username` VARCHAR(100) NOT NULL,
    `password` VARCHAR(100) NOT NULL,
    `first_name` VARCHAR(191) NULL,
    `last_name` VARCHAR(191) NULL,
    `phone_number` VARCHAR(20) NULL,
    `email` VARCHAR(255) NOT NULL,
    `is_status` BOOLEAN NOT NULL DEFAULT true,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `user_employee_user_code_key`(`user_code`),
    UNIQUE INDEX `user_employee_username_key`(`username`),
    UNIQUE INDEX `user_employee_email_key`(`email`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE UNIQUE INDEX `permission_lv_code_key` ON `permission`(`lv_code`);

-- AddForeignKey
ALTER TABLE `permission_access_privilege` ADD CONSTRAINT `permission_access_privilege_permission_id_fkey` FOREIGN KEY (`permission_id`) REFERENCES `permission`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `permission_access_privilege` ADD CONSTRAINT `permission_access_privilege_mst_access_privilege_id_fkey` FOREIGN KEY (`mst_access_privilege_id`) REFERENCES `mst_access_privilege`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_employee` ADD CONSTRAINT `user_employee_prefix_name_id_fkey` FOREIGN KEY (`prefix_name_id`) REFERENCES `prefix_name`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_employee` ADD CONSTRAINT `user_employee_permission_id_fkey` FOREIGN KEY (`permission_id`) REFERENCES `permission`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `permission` ADD CONSTRAINT `permission_user_emp_update_id_fkey` FOREIGN KEY (`user_emp_update_id`) REFERENCES `user_employee`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
