import { ApiProperty } from '@nestjs/swagger';

export class CarInsurance {
  @ApiProperty({
    example: 1,
    description: 'The unique identifier of the car insurance',
  })
  id: number;

  @ApiProperty({
    example: 1,
    description: 'The unique user_id associated with the car insurance',
    type: Number,
  })
  user_id: number;

  @ApiProperty({
    example: 1,
    description: 'The unique car_brand_id associated with the car insurance',
    type: Number,
  })
  car_brand_id: number;

  @ApiProperty({
    example: 1,
    description: 'The unique car_model_id associated with the car insurance',
    type: Number,
  })
  car_model_id: number;

  @ApiProperty({
    example: 1,
    description: 'The unique car_submodel_id associated with the car insurance (optional)',
    type: Number,
    required: false,
  })
  car_submodel_id?: number;

  @ApiProperty({
    example: 1,
    description: 'The unique insurance_type_id associated with the car insurance',
    type: Number,
  })
  insurance_type_id: number;

  @ApiProperty({
    example: 1,
    description: 'The unique license_plate_province_id associated with the car insurance',
    type: Number,
  })
  license_plate_province_id: number;

  @ApiProperty({
    example: 1,
    description: 'The unique car_code_id associated with the car insurance',
    type: Number,
  })
  car_code_id: number;

  @ApiProperty({
    example: 1,
    description: 'The unique car_insurance_status_id associated with the car insurance',
    type: Number,
  })
  car_insurance_status_id: number;

  @ApiProperty({
    example: 2023,
    description: 'The year of the car associated with the insurance',
  })
  year: number;

  @ApiProperty({
    example: 500000.00,
    description: 'The original insurance value of the car (optional)',
    type: Number,
    required: false,
  })
  original_insurance_value?: number;

  @ApiProperty({
    example: 450000.00,
    description: 'The current insurance value of the car',
    type: Number,
  })
  current_insurance_value: number;

  @ApiProperty({
    example: 'sent',
    description: 'The reminder status of the car insurance',
  })
  reminder_status: string;

  @ApiProperty({
    example: 'proceed',
    description: 'The job status of the car insurance',
  })
  job_status: string;

  @ApiProperty({
    example: true,
    description: 'Indicates whether the car insurance is active',
  })
  is_active?: boolean;

  @ApiProperty({
    example: '2024-09-03T12:34:56Z',
    description: 'The date and time when the car insurance was created',
  })
  created_at: Date;

  @ApiProperty({
    example: '2024-09-03T12:34:56Z',
    description: 'The date and time when the car insurance was last updated',
  })
  updated_at: Date;
}