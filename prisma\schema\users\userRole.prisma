model UserRole {
    id                 Int                  @id @default(autoincrement())
    code               String               @unique
    name               String
    is_active          <PERSON>ole<PERSON>              @default(true)
    created_at         DateTime             @default(now())
    updated_at         DateTime             @updatedAt
    userRolePermission UserRolePermission[]
    user               User[]
    userRoleLog        UserRoleLog[]

    @@map("user_role")
}
