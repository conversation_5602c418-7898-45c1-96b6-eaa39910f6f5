import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';

import { InsuranceAddonService } from '@prisma/client';

@Injectable()
export class InsuranceAddonServiceService {
  public static MODEL_NAME = 'insuranceAddonServiceService';
  constructor(private prisma: PrismaService) { }

  async findAll(): Promise<InsuranceAddonService[]> {
    return await this.prisma.insuranceAddonService.findMany();
  }

  async findOne(id: number): Promise<InsuranceAddonService> {
    return this.prisma.insuranceAddonService.findUnique({ where: { id, is_active: true } });
  }
}
