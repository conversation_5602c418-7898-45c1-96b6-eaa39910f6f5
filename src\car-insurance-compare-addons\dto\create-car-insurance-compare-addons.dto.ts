import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { <PERSON>Array, IsBoolean, IsInt, IsOptional, ValidateNested } from "class-validator";

export class CreateCarInsuranceCompareAddonsDto {
  @IsInt()
  @IsOptional()
  @Transform(({ value }) => Number(value))
  @ApiProperty({ example: 1, description: "The unique car_insurance_compare_id of the car insurance compare addons", type: Number, })
  car_insurance_compare_id?: number;

  @IsInt()
  @IsOptional()
  @Transform(({ value }) => Number(value))
  @ApiProperty({ example: 1, description: "The unique insurance_addon_service_id of the car insurance compare addons", type: Number, })
  insurance_addon_service_id?: number;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ example: 1, description: "The status active of the car insurance compare addons", type: Boolean, })
  is_active?: number;
}
