import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsNotEmpty } from 'class-validator';

export class CreateUserRolePermissionDto {
  @IsInt()
  @IsNotEmpty()
  @ApiProperty({ example: 1, description: 'The ID of the permission', type: Number })
  permission_id: number;

  @IsInt()
  @IsNotEmpty()
  @ApiProperty({ example: 1, description: 'The ID of the user role', type: Number })
  user_role_id: number;

  @IsBoolean()
  @ApiProperty({ example: true, description: 'Indicates whether the permission is granted', type: Boolean })
  is_granted: boolean;

}