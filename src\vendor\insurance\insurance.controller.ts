import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  UploadedFile,
  UseInterceptors,
  ParseIntPipe,
  Query,
} from "@nestjs/common";
import { InsuranceService } from "./insurance.service";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";
import { FileInterceptor } from "@nestjs/platform-express";
import { diskStorage } from "multer";
import { join } from "path";
import { FileService } from "@common/services/file.service"; // Assuming this is where your utility function is
import { ConvertService } from "@common/services/convert.service";
import { PaginationService } from "@common/services/pagination.service";
import { Insurance } from "./entities/insurance.entity";
import { ItemFilterDto } from "./dto/item-filter.dto";

@ApiTags("vendor-insurance")
@Controller("vendor-insurance")
export class InsuranceController {
  constructor(
    private readonly carBrandService: InsuranceService,
    private readonly paginationService: PaginationService,
    private fileService: FileService
  ) { }

  @Get("pagination")
  @ApiOperation({ summary: "Get a list of all insurance with pagination" })
  @ApiQuery({ name: "page", required: false, type: Number, example: 1 })
  @ApiQuery({ name: "perPage", required: false, type: Number, example: 10 })
  @ApiQuery({ name: 'filter[place]', required: false, type: String, description: 'Filter by place (1,2)', example: "1,2" })
  @ApiQuery({ name: 'filter[insurance]', required: false, type: String, description: 'Filter by place (1,2)', example: "1,2" })
  @ApiQuery({ name: 'filter[firstDamages]', required: false, type: String, description: 'Filter by place (1,2)', example: "1,2" })
  @ApiQuery({ name: 'filter[insuranceAddonService]', required: false, type: String, description: 'Filter by place (1,2)', example: "1,2" })
  @ApiQuery({ name: 'filter[minCurrentInsuranceValue]', required: false, type: String, description: 'Filter by minCurrentInsuranceValue (150000)', example: 150000 })
  @ApiQuery({ name: 'filter[maxCurrentInsuranceValue]', required: false, type: String, description: 'Filter by maxCurrentInsuranceValue (400000)', example: 400000 })
  @ApiQuery({ name: "sort", required: false, type: String, example: "-name" })
  @ApiResponse({
    status: 200,
    description: "The list of insurance has been successfully retrieved.",
    type: [Insurance],
  })
  // TODO: Wait connect api insurance vendor
  async pagination(
    @Query("page", ParseIntPipe) page: number = 1,
    @Query("perPage", ParseIntPipe) perPage: number = 10,
    @Query("filter") filter: any = {},
    @Query("sort") sort: string = ""
  ) {
    const where: any = {};
    const orderBy: any = {};
    const total = 35;

    // Handle filter
    const { place, insurance, firstDamages, insuranceAddonService, minCurrentInsuranceValue, maxCurrentInsuranceValue } = filter;


    if (place != null) {
      where.place = {
        in: place.split(','),
      };
    }
    if (insurance != null) {
      where.insurance = {
        in: insurance.split(','),
      };
    }
    if (firstDamages != null) {
      where.firstDamages = {
        in: firstDamages.split(','),
      };
    }
    if (insuranceAddonService != null) {
      where.insuranceAddonService = {
        in: insuranceAddonService.split(','),
      };
    }

    if (minCurrentInsuranceValue != null && maxCurrentInsuranceValue != null) {
      where.current_insurance_value = {
        gte: minCurrentInsuranceValue,
        lte: maxCurrentInsuranceValue,
      };
    }

    // Handle sorting
    const sorting = sort;

    if (sorting) {
      const sortField = sorting.replace('-', '');
      const sortOrder = sorting.startsWith('-') ? 'desc' : 'asc';
      orderBy[sortField] = sortOrder;
    }

    console.log(where);
    console.log(orderBy);

    const insuranceMaster = [
      {
        "insurance_image": "https://s3-alpha-sig.figma.com/img/cb96/6e06/dc0dc59d67faf785b9d75c80f2bd88c6?Expires=1726444800&Key-Pair-Id=APKAQ4GOSFWCVNEHN3O4&Signature=fTDN41XVi7T6UwP2R2KTXGUU7AFxD2gYxRNQ9neKeSRgxYHHE-eds72DrvH77geeNMntLAUUzuJzMyHepdQ2KM9QNkt~~bNnPNA8pMRqa~fbcpIGL8h-YIuFt3wW0M5HvC4nY2sTVerVtYslCx3Oq7y66wD3LEeaH0wexx1v6viu8aCtWlkvxIhweO4TEE94QmQmvgNhHMIL~iJmHmanE4HQDfCz8-yZ5K529G7VHX~BnyGNgqhXsMna-f~WBqECc~gyLWCBx9jJp2xmZm4yk0hUuyuJ40gjqjjzUkvOfKBHRpe2NIEXfXUorL1A4f6Zr6h4EIGqpzH5f9ASTVT9~A__",
        "insurance_name": "ธนชาตประกันภัย",
        "insurance_type": "ชั้น 1",
        "repair_type": "ซ่อมห้าง",
      },
      {
        "insurance_image": "data:image/png;base64,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",
        "insurance_name": "กรุงเทพประกันภัย",
        "insurance_type": "ชั้น 1",
        "repair_type": "ซ่อมอู่",
      },
      {
        "insurance_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAABkCAYAAABO6zhfAAAAAXNSR0IArs4c6QAAD1pJREFUeF7tXXtc1UUWPwaf3SQVXEtWFHzsIj4C1MR8G2YqvvAdvv1gmQ9U0gIfiRqJSVpqaorh4qM08RGKikqZ+MjUSGBRCU0BH4srqITm1pr7+Q479zP87u/CvVdv997fnfOPMr8zc2fO+f7OzJw5c35VyEx69OjRIzOrymoWkECVKlWqmNOsWZXwQxIA5ojbcnUkACwnW7toWQLALtRkuU5KAFhOtnbRsgSAXajJcp2UALCcbO2iZQkAu1CT5TopAWA52dpFyxIAdqEmy3VSAsBysrWLliUA7EJNluukBIDlZGsXLUsA2IWaLNdJCQDLydYuWpYAMEJN/334kBav3arKOW3sQKrmUpVK7/9CyxN2qvJEvhFCzk5OVHjrNn26bZ8qz5xJI4zoyZNnkQCoRKZ51wrppZEzmPJEgkJTN8ZSG78mlJ6dSy+NmEEAiki13GowHp+GnnQiPZsmzV9BP16+Wo6nccN6tHr+VGrfqvmT164RLUoAVCKkHqEz6ejpLD2uD2dPoAnD+rLypj1CKe96oR7PF8vfob5d27Hy514cRPfuP9DjObZ1GbVq7m2EqizDIgFQgVx3HTpGI6Yv0uPA25q6IZaVr9yURBGxcXo8r3R4gZLWvMvK314cR6s2J+nxhPQJpPWL3rKMZo1sVQLAgKBgzmu/OJge/OdXPY5vPlvKTD+mhYaBI1VbOJ+ynurXdafs3DwKGDhJlefmd9vZ+sGaJAFgQPrj3/mINiel6j31a9KITiZ+zMrbDplCmRd+0uMZEtSZNsRGsvI67YfS3Z/v6fGEjxlIMW+Ns6bu2W9LAKiooOh2CXl2HqaqnKy96+hvXh6Uc7mAWvaboMpz/cQ2cqv+DH198iz1eX2OKk/K+kXUOcBPAsDqElDpwMLVn9HCTz7XewLFAwCgucsSaGl8oh5PYNsWtHfdQlY+ad4KSth5QI/ntaFBtGJumE0MXVoAFTV0DAlnWzslzZk4nPh+3dCqXuRx8e2tquR3w8fSW+OGSADYhARUOmFIcdivjx3Ugzl9sEBUI+wOsEuA/6Bpz1BVnsuHN5P7szVtYvjSAijUcCrzAnPqqFHxmV309J//RAk7DjCnjhrdz9rLit9fu5XeXbmpQh5bQIAEgEILhuZ/sHHlGuIBOAASkCEeP59GdHJ72S7CFkgCwEgAuNeqSZe/2VyhcoO6tKEdK+cxnojFcbRSxfkTNjKYYiPH24LuWR8kAIwEQN+ubemL5XMrVG7k+BCaN2UU4+kZOpPSVFzI4iLRFlAgAWAkAETFGXISiTzG7CQkAGxBAoo+wLOXoeLd82/SiOAFBOFk71L+Db3eizy7Dh6jUpXDH5HHFoYvLYAtaMGKfZAAsKLwbeGnHQoAWJQNCpuvei5vaWU84/I09erShho3qEfN/l6fAtu1ZOcF1iaHAgCEjSPcETMWsXnc0gRFD3ilA+HcH+cItkgOBwCuBMT4LTDgqXtcRcEhlBz3ntXCvEzpv8MCAELa8/W39Oq090yRV4W8iBNcvWAqjQzu9sTatHRDDg0ACBenfrOXxqs6bUwRPuL6NsRG2KypNzQWhwcABIPTvcFhC8wGAfwDOAW0dniXKYDlvBIAgtSWJeykqGUJeuHdlQn2h91rWOi3PZIEgEJrCOJ8Y+5HqgEhagqeMW4IRYePtUfdsz5LAKioDu5g+Pszc/QDPpXs1o7rf1zkSQBUIEFMCVggVkQ8RuBxFWGt+hIAlUge3sNXp0WrhnajqgSAidC1x1Sxl/Kv07hZSwnhYkqSAHAAAPAhjn57MW1PSSs3YgkABwIAhrrvyCmaFr2KrhXeYiOXi0AHAwCGi4MkHCjhYMnWQrxMVIfcBpoqMM4P7+H0mDXs6vj5A+vNbcbq9eQu4DFVcPjkWbpTUkoDund8zJasU10C4AnIHbd/XW0guMOcoWgGAOJFjGF9AineyokXzFGGNepoBgAjpsfQrkPHmQztfWH2RwJBMwBApg6eyMmWbt/+kco057c0AQAEdeAiBqczO1dTM+/65sjD4epoAgBL4hPZOT4IgZhndq12OEWaO2BNAEBcAE4Y1oc+nD3RXHk4XD2bA4AyOUPyuoXUtW0LphgxLQu/q49y8Sau2gJwS/Jhivnkc8KhjkgI1wa/MmSbZ/1csyWZiu6U6Kog6BPpXVAPWcLUCGnjPk3cVy4hJKKEXxsSRIODOqvWQyYyWDD8nphsEokmAWhlFlFl8gnleYQow8pyEdk8AAylZYmZMY7Cxw5kOhAXgGuj36RR/cuicpHICd46OGsMEZSKhI642s3Ju9sYna/fUD2eCUR8buhGsMiTsv596hzgqytCZDJyCYlAU/4m+JF1jGcV0TQAYM6PnsnSBWhyAODwBYrhhNDruPfeZH+KiM9N3UB13Z9l5WqZPCBEZdrX+h7u5dy5ifvTaEzEYtZGGz8fatzQk0UHiSnhlFPN/iOnaFDYgnK6Qz/4oRF/gOhhHCBxQjYS5TEz3nwlICLHv0rzpoxm1TQNAAxQnNN5QgW1jBt8ehABoDSHPMYvpHcgM98wxyDlnQA4juBA4sSVIpr62UviadmGsoTQ8PzdOLFNx/+X1gN0SSWRJXTHqnksQTQIQSWwDpx+2L2WfBrW0/2NjKRRyzbQ2uhw3WUSKBmxiTzHANoqObvbMQCwZsseZrpBPKWKmllGssWeXQKoZ+gsxos3p+DoFp1g1a5xwwJ07/gC4+n9+hzd9GCM82jTl6lMKZxEsIkgVMsAKiaMjI0YT2GjglkzaFNJ/Ar5nZ/vkUf7obrH3Lpp3gKcy82j1kJ6VQycm39k1+YZt2FiJ48M1sXtiRk6ITW1RA6YTzEPKy2NCACeHv7Q8XTVKKDKABAVNooePnxIrtWr6RQtJpSuLJWcoec8+bTmAQABi28U5uFTmTlM7vDyZeX8RJinQVA6/7/yLVbL02MIAGMH9mDXukDdxkQYdXnUkAXgABHXFk8CAHyB63AA4AKF+YY1wOJuavQqPdOJN2/mGyG6cnHdgNs7mE5gQXiSRvE5B0/i/iM0JqIsEzgIddxqlF3jvlNyr1yo+JMGAL9TiHxEPM282pbOIQAQPCGKDh3/vpySKzOdyvAsUcFqqdlbD5hE5y7msd/gbYt1xKRP4Ek7nalbb+BvQwDAtwTgkcSCky8ijbEA4vcF0L5S0XxtoSzH4pAvOOFTwIKUk935AXjH1bZwBWlbqFbNGoxleswnzHEiknIHgEUgFojcuYI8vjw/P1K4ibl++fUuEQDKnL7GAkAtUNQQAKZGr6RPt+1nw4CFmxc2imUjPXjse4pd94VuKoLlOrt7rW64omXAuDbGRrI7juLOAcx2CwBlxm68ScjTzwkC6j8xqkIA4OGCjzfR4jj17/3wyu1aNqOvNn7A/lRuD8V53BIAgJ8Alkgtpbw4OHHngHJDXzIpJxB7BgAGIqJc9PzxQYbOWkJbkw+zPzsF+NKB/6/ulUKoyEOHvL7L35lMzYXTQ5Hf0gBAX5WWStl/nqNYLEcdLFaVhKlAdCfbrQXAwOAi5YPBCp3Pc3zQyMfPASAunPSkQsS+1gVv3qFj6eTk9BS98Lw3dWrtS68N7aXGzj4F8+OVApYOjqd/h2t5afx2HT/3RKIAW05OYjkvm7UkXufdU+srLB78H+nnLhK2wXA0Ycdi6PwA7cIzmZiSRvBCgtq3asa2xWIfZ4wbXOHNZZs7C1DVhiy0mAQkACwmWvtoWALAPvRksV5KAFhMtPbRsM0CYOlHy6m0VP9rW7YuVl/f52lg/3563cQiUW1xqDYenAC6P+tmVNoZLFb5wZI5srFZACyIjjFnPFav4+rqSuFTJ+v1A1taY28SAwDwXeBACa7oir4vAIfZ1r1lW2G4kk1NUScB8IQhIwIAjiP+aTgOAGxrsYdHGBoPXBG7wJ8j6RSPABLbQZwCdy/DJYxP1uJvHueAsDd8otarrjsV3iqu1IrYBQDGjC77svaOnV/qpoUunTuxsiNpR41SYbVqz1DH9u0p5eAho/jNZRIBIL71/P9wCQe286dTGTnMLY2QdmdnJ+aTwLkEbh0jfnDP1yfplQ6taEVUGHOIceuB+riMCicY+BEviWkAZdxywArgBHVz0leVTjt2AYDIt6fTye9OU27uRcIcC3rwoOxDzL/99ivVqlWLMjKyqEOHdvT777/T8ePf0ojhIfSvwkI6ciSNruTlk5urKwUH92F8aANCP3AglQIDu1B29jmC4jIyMhnPlSv5VK9eXTp9+gx5enqSe+3n6Nr1G+RZry5VdalKSUnJFBTUnbKzz5OLS1WqU6cO4/3p8hXWDp8C1ABQo0U/QngX/PfwQkJpR09nUqcAP/ry0DHKuXyVOZ6gVOQnwkGPGgDgBZw9cTgDAM4vQLAYSGABAHh51NYOAN6cNoX+kbCR/P39qIlPY6bYO3fuskG7uLjQ/pSyjzMO6N+PiovLPvPeoIEXUyS3EiIAoCQ3N1fak7yP1anp5ka5Fy9RVtY/qW/fXqxeXl4e1a79HDVt2pSB52xGJt29e5eC+/Wh/SkHGdDOfJ9OvXv1JGdnZyoouErpP5ytFADw/eMgC2/4irmTy3kioUi8tXibefQR3nw1AIDXy8OdBcByADTy8qDJ81cwYGjKAnTv9jIdTP2KGtT3Im9vbyoqKqLi4mLy8vKk/PwC8vHxofPnL5CHx1+pevUadPzECWrVsgXdulXEAHAh50cGlFYt/Sk//yp7a/E3nvv6NqfCwpt08+a/yd/fl27fvs3KbxUVkUtVF/LxaUxViCi/oIDu3/+FWrb0p737UujlroF07do1Ki0tZb8P6wFgKi0ATjCzL+axtxmHWbjEMnN8CDukcnZ6ii4V3KBv08/RnEnD2fGvIQDgPMDJ2YliVn/OAIJTxADfJuUAgLGiPUwt78dtpZKSe5V+n9gupgBz52Nr1BMBAB897jLguwBRU0Yz3z4Uh/MIvyYN2QUWcSGIj1XjlHPjB5Hs3w/XJ7LwNVx6xdF1gJ8P4cwAz7GtRNwB0tPyj1wj5gHlABSuxsXMCNXGItAaijT3Nw1tA81tz9L1bNYCaM0RZGlFmtu+zQLA3AHJeqZJQALANHlpjlsCQHMqNW1AEgCmyUtz3BIAmlOpaQOSADBNXprjlgDQnEpNG5AEgGny0hy3BIDmVGragCQATJOX5rglADSnUtMGJAFgmrw0xy0BoDmVmjYgCQDT5KU5bgkAzanUtAFJAJgmL81xmwuA/wEyNbDdcPpZZQAAAABJRU5ErkJggg==",
        "insurance_name": "กรุงเทพประกันภัย",
        "insurance_type": "ชั้น 1",
        "repair_type": "ซ่อมอู่",
      },
    ]

    let mockupData: any = {
      "is_popular_package": false,
      "is_named_driver": true,
      "is_excess": true,
      "price": "16,000.00",
      "vehicle_damage": "430,000",
      "vehicle_loss_or_fire": "430,000",
      "external_person_life_per_person": "500,000",
      "external_person_life_per_case": "10,000,000",
      "external_person_property": "600,000",
      "excess_property_damage": "ไม่มี",
      "personal_accident": "50,000 (1+5 คน)",
      "medical_expense_per_person": "50,000 (1+5 คน)",
      "driver_bail": "200,000",
      "current_insurance_value": "430,000.00",
      "marketing_cost": "1,504.49",
      "excess": "3,000.00",
      "insurance_addon_service": [
        {
          id: 1,
          name: "ช่วยเหลือฉุกเฉิน",
        },
        {
          id: 2,
          name: "ภัยธรรมชาติ",
        },
      ],
      "created_at": "2024-08-22T12:34:56Z",
      "updated_at": "2024-08-22T12:34:56Z"
    };

    let data: any = [];

    const start = (page - 1) * perPage + 1;
    let end = page * perPage;
    if (end >= total) {
      end = total;
    }

    for (let index = start; index <= end; index++) {
      data.push(JSON.parse(JSON.stringify({
        id: index,
        ...insuranceMaster[Math.floor(Math.random() * 3)],
        ...mockupData
      })));
    }
    data[0].is_popular_package = true;
    return {
      data,
      "meta": {
        "total": total,
        "lastPage": page - 1,
        "currentPage": page,
        "perPage": perPage,
        "prev": page - 1,
        "next": (page * perPage) >= total ? null : page + 1
      }
    };
  }
}