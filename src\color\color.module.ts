import { Modu<PERSON> } from '@nestjs/common';
import { ColorController } from './color.controller';
import { ColorService } from './color.service';
import { PrismaModule } from 'src/prisma/prisma.module';
import { PaginationService } from "@common/services/pagination.service";
import { HandleErrorService } from '@common/services/handle-error.services'; 

@Module({
  controllers: [ColorController],
  providers: [ColorService,PaginationService,HandleErrorService],
  imports: [PrismaModule],
})
export class ColorModule {}
