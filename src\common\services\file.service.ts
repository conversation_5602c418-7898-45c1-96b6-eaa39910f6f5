import { unlinkSync } from "fs";
import { extname, join } from "path";

export class FileService {
  private readonly baseDir: string;

  constructor(baseDir: string = join(__dirname, "..", "..", "..","..","public")) {
    this.baseDir = baseDir;
  }

  public static generateFileName(file: Express.Multer.File): string {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    const ext = extname(file.originalname);
    return `${uniqueSuffix}${ext}`;
  }

  deleteFile(relativePath: string): void {
    const fullPath = join(this.baseDir, relativePath);
    try {
      unlinkSync(fullPath);
    } catch (err) {
      console.error(`Error deleting file: ${fullPath}`, err);
    }
  }
}
