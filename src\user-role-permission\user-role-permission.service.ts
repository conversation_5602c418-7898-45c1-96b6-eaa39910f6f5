import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { UserRolePermission } from '@prisma/client';
import { Prisma } from '@prisma/client';
import { join } from 'path';
import { CreateUserRolePermissionDto } from './dto/create-user-role-permission.dto';
import { UpdateUserRolePermissionDto } from './dto/update-user-role-permission.dto';
import { HandleErrorService } from "@common/services/handle-error.services";

@Injectable()
export class UserRolePermissionService {
  public static PATH_FILE = 'userRolePermission';
  constructor(private prisma: PrismaService,
    private readonly handleErrorService: HandleErrorService) {}

    async create(prisma:any,user_role_permission:any, roleId: number) {
      return  await prisma.userRolePermission.createMany({
        data: user_role_permission.map((permission:any) => ({
          permission_id: permission.permission_id,
          user_role_id: roleId,
          is_granted: permission.is_granted
        })),
      });
    }

    async update(prisma:any ,user_role_permission:any, roleId: number) {

      await prisma.userRolePermission.deleteMany({
        where: { user_role_id: roleId },
      });

      await prisma.userRolePermission.createMany({
        data: user_role_permission.map((permission:any) => ({
          permission_id: permission.permission_id,
          user_role_id: roleId,
          is_granted: permission.is_granted
        })),
      });
  }  

  async findAll(): Promise<UserRolePermission[]>  {
    return await this.prisma.userRolePermission.findMany();
  }

  async findOne(id: number): Promise<UserRolePermission> {
    return this.prisma.userRolePermission.findUnique({ where: { id,is_active:true },include: { permission: true }, });
  }

  async remove(id: number): Promise<UserRolePermission> {
    return this.prisma.userRolePermission.update({
      where: { id },
      data: {
        is_active: false,
      },
    });
  }
}
