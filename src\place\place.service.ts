import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';

import { Place } from '@prisma/client';

@Injectable()
export class PlaceService {
  public static MODEL_NAME = 'place';
  constructor(private prisma: PrismaService) { }

  async findAll(): Promise<Place[]> {
    return await this.prisma.place.findMany();
  }

  async findOne(id: number): Promise<Place> {
    return this.prisma.place.findUnique({ where: { id, is_active: true } });
  }
}
