import { ApiProperty } from "@nestjs/swagger"

export class Customer {
   @ApiProperty({
      example: 1,
      
    })
  id :number    
   @ApiProperty({
      example: true,
      
    })                          
    user_id:string    
     @ApiProperty({
        example: "",
        
      })          
  registration_type_id:string  
   @ApiProperty({
      example: "",
      
    })            
  prefix_name_id:string     
   @ApiProperty({
      example: "",
      
    })               
  license_plate_province_id:string        
   @ApiProperty({
      example: "",
      
    }) 
  business_type_id:string       
   @ApiProperty({
      example: "",
      
    })           
  phone_number:string   
   @ApiProperty({
      example: "",
      
    })                  
  first_name:string        
   @ApiProperty({
      example: "",
      
    })            
  last_name:string  
   @ApiProperty({
      example: "",
      
    })                     
  business_name:string        
   @ApiProperty({
      example: "",
      
    })           
  address:string        
   @ApiProperty({
      example: "",
      
    })                  
  car_registration_number:string  
   @ApiProperty({
      example: "",
      
    })         
  is_active:string   
   @ApiProperty({
      example: new Date(),
      
    })                    
  created_at:Date        
   @ApiProperty({
      example: new Date(),
      
    })               
  updated_at:Date      
   @ApiProperty({
      example: "",
      
    })                 
  user:string         
   @ApiProperty({
      example: "",
      
    })
  registrationType:string      
   @ApiProperty({
      example: "",
      
    })   
  prefixName:string      
   @ApiProperty({
      example: "",
      
    })        
  licensePlateProvince:string    
   @ApiProperty({
      example: "",
      
    })
  businessType:string       

}