import { HttpAdapterHost, NestFactory, Reflector } from '@nestjs/core';
import { AppModule } from './app.module';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { PrismaClientExceptionFilter } from './prisma-client-exception.filter';
import { NEST_APP_PORT, NEST_APP_VER } from './configs/server';
import { ValidationPipe } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  // app.useGlobalPipes(new ValidationPipe({ whitelist: true }));
  app.useGlobalPipes(new ValidationPipe({ transform: true, whitelist: true, forbidNonWhitelisted: false }));
  app.setGlobalPrefix('api/v1', {
    exclude: ['/'],
  });

  const config = new DocumentBuilder()
    .setTitle('Test API')
    .setDescription('Test API description')
    .setVersion(NEST_APP_VER)
    .addBearerAuth()
    .setExternalDoc('Postman Collection', '/docs-json')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  const { httpAdapter } = app.get(HttpAdapterHost);
  app.useGlobalFilters(new PrismaClientExceptionFilter(httpAdapter));
  app.enableCors();

  await app.listen(NEST_APP_PORT || 3000);


  const server = app.getHttpServer();
  const router = server._events.request._router;
  console.table(
    router.stack
      .map((layer) => {
        if (layer.route) {
          return {
            path: layer.route?.path,
            method: Object.keys(layer.route?.methods).join(', ').toUpperCase(),
          };
        }
      })
      .filter((route) => route !== undefined),
  );
  console.info(`start port => ${NEST_APP_PORT}`)
}
bootstrap();
