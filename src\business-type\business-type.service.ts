import { Injectable } from "@nestjs/common";
import { BusinessType } from "@prisma/client";
import { PrismaService } from "src/prisma/prisma.service";

@Injectable()
export class BusinessTypeService {
  //   public static Model = "businessType";
  constructor(private prisma: PrismaService) {}

  async findAll(): Promise<BusinessType[]> {
    return await this.prisma.businessType.findMany({
      where: {
        is_active: true,
        AND: [
          {
            id: {
              not: 1,
            },
          },
        ],
      },
    });
  }
}
