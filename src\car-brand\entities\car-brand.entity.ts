import { ApiProperty } from '@nestjs/swagger';

export class CarBrand {
  @ApiProperty({
    example: 1,
    description: 'The unique identifier of the car brand',
  })
  id: number;

  @ApiProperty({
    example: 'B001',
    description: 'The unique code of the car brand',
  })
  code: string;

  @ApiProperty({
    example: 'Toyota',
    description: 'The name of the car brand',
  })
  name: string;

  @ApiProperty({
    example: '/uploads/carBrand/toyota.png',
    description: 'The image path for the car brand',
  })
  image_path: string;

  @ApiProperty({
    example: true,
    description: 'The status of the car brand (e.g., active or inactive)',
  })
  is_status: boolean;

  @ApiProperty({
    example: true,
    description: 'Indicates whether the car brand is active',
  })
  is_active?: boolean;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the car brand was created',
  })
  created_at: Date;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the car brand was last updated',
  })
  updated_at: Date;
}
