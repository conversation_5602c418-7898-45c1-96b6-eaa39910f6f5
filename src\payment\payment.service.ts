import { ConflictException, Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
// import { Permission } from './entities/car-model.entity';
import { HandleErrorService } from "@common/services/handle-error.services";
import { Permission, Prisma } from '@prisma/client';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { UpdatePaymentDto } from './dto/update-payment.dto';


@Injectable()
export class PaymentService {
  public static MODEL_NAME = 'payment';
  constructor(private prisma: PrismaService,
    private readonly handleErrorService: HandleErrorService) {}

  async create(data: CreatePaymentDto) {
    return this.prisma.$transaction(
      async (prisma: Prisma.TransactionClient) => {
        const {...createData} = data;
        try {
          const paymentCreated = await prisma.payment.create({
            data: {
              ...createData,
            }
          });
          
         
          
        } catch (error) {
          if (error.code === 'P2002') {
            throw new ConflictException(
              'Unable to generate a unique lv_code after multiple attempts.',
            );
          }
          throw error;
        }
      },
    );
    
  }

  async findAll()/* : Promise<Permission[]>   */{
    return await this.prisma.payment.findMany({
      where: {
        is_active: true
      },
    });
  }

  async findOne(id: number)/* : Promise<Permission> */ {
    return this.prisma.payment.findUnique({ 
      where: { id,is_active:true },
    });
  }

  
  async update(user_id:number,id: number, data: UpdatePaymentDto) {
    return this.prisma.$transaction(
      async (prisma: Prisma.TransactionClient) => {
        let {  ...updateData } = data;
        
        updateData['user_emp_update_id'] = user_id;
        
        try {
          const updatedPermission = await prisma.payment.update({
            where: { id },
            data: updateData,
          });
         
  
          return updatedPermission;
        } catch (error) {
          this.handleErrorService.handlePrismaError(error);
        }
      }
    );
  }

  async remove(id: number): Promise<Permission> {
    const existData = await this.prisma.payment.findUnique({ where: { id, is_active: true } });
    const date = Date.now();
    return this.prisma.permission.update({
      where: { id },
      data: {
        is_active: false,
      },
    });
  }

 
}
