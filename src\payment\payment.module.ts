import { FileService } from "@common/services/file.service";
import { HandleErrorService } from '@common/services/handle-error.services';
import { PaginationService } from "@common/services/pagination.service";
import { Module } from "@nestjs/common";
import { PrismaModule } from "src/prisma/prisma.module";
import { PaymentController } from "./payment.controller";
import { PaymentService } from "./payment.service";

@Module({
  controllers: [PaymentController],
  providers: [PaymentService, FileService, PaginationService,HandleErrorService],
  imports: [PrismaModule],
})
export class PaymentModule {}
