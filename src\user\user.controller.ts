import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  UploadedFile,
  UseInterceptors,
  ParseIntPipe,
  Query,
  Res,
} from "@nestjs/common";
import { UserService } from "./user.service";
import { AdminService } from "../admin/admin.service";

import { User } from "./entities/user.entity";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";
import { FileInterceptor } from "@nestjs/platform-express";
import { diskStorage } from "multer";
import { CreateUserDto } from "./dto/create-user.dto";
import { CreateUserAdminDto } from "./dto/create-user-admin.dto";
import { UpdateUserAdminDto } from "./dto/update-user-admin.dto";


import { UpdateUserDto } from "./dto/update-user.dto";
import { join } from "path";
import { FileService } from "@common/services/file.service"; // Assuming this is where your utility function is
import { ConvertService } from "@common/services/convert.service";
import { PaginationService } from "@common/services/pagination.service";
import { HashPasswordService } from "@common/services/hash-password.services";
import { UserRole } from "src/user-role/entities/user-role.entity";
import { ExcelService } from "@common/services/excel.service";



@ApiTags("user")
@Controller("user")
export class UserController {
  constructor(

    private readonly userService: UserService,
    private readonly adminService: AdminService,
    private readonly paginationService: PaginationService,
    private readonly hashPasswordService: HashPasswordService,
    private readonly excelService: ExcelService,

    private fileService: FileService
  ) { }

  @Post()
  @ApiOperation({ summary: "Create a new car brand" })
  @ApiConsumes("multipart/form-data")
  @ApiResponse({
    status: 201,
    description: "The car brand has been successfully created.",
    type: CreateUserDto,
  })
  @UseInterceptors(
    FileInterceptor("image_path", {
      storage: diskStorage({
        destination: `./public/uploads/${AdminService.MODEL_NAME}`,
        filename: (req, file, callback) => {
          callback(null, FileService.generateFileName(file));
        },
      }),
    })
  )
  async create(
    @UploadedFile() image_path: Express.Multer.File,
    @Body() createUserAdminDto: CreateUserAdminDto
  ) {
    // Convert string fields to boolean

    createUserAdminDto.is_active = ConvertService.stringToBoolean(
      createUserAdminDto.is_active
    );

    createUserAdminDto.user_type_id = createUserAdminDto.user_type_id ? Number(createUserAdminDto.user_type_id) : null;
    createUserAdminDto.user_role_id = createUserAdminDto.user_role_id ? Number(createUserAdminDto.user_role_id) : null;
    createUserAdminDto.parent_id = createUserAdminDto.parent_id ? Number(createUserAdminDto.parent_id) : null;
    createUserAdminDto.prefix_name_id = createUserAdminDto.prefix_name_id ? Number(createUserAdminDto.prefix_name_id) : null;

    // TODO: Wait Hash password match register and login
    if (createUserAdminDto.password) {
      const hashedPassword = await this.hashPasswordService.hashPassword(createUserAdminDto.password);
      createUserAdminDto.password = hashedPassword
    }

    return await this.userService.createUserAndAdmin(
      {
        username: createUserAdminDto.username,
        password: createUserAdminDto.password,
        phone_number: createUserAdminDto.phone_number,
        email: createUserAdminDto.email,
        is_active: createUserAdminDto.is_active,
        user_role_id: createUserAdminDto.user_role_id,
        user_type_id: createUserAdminDto.user_type_id,
        parent_id: createUserAdminDto.parent_id,
      },
      {
        user_id: null,
        prefix_name_id: createUserAdminDto.prefix_name_id,
        first_name: createUserAdminDto.first_name,
        last_name: createUserAdminDto.last_name,
        is_active: createUserAdminDto.is_active,
      },
      image_path
    );
  }

  @Put(":id")
  @ApiOperation({ summary: "Update a car brand by ID" })
  @ApiConsumes("multipart/form-data")
  @ApiResponse({
    status: 200,
    description: "The car brand has been successfully updated.",
    type: UpdateUserDto,
  })
  @UseInterceptors(
    FileInterceptor("image_path", {
      storage: diskStorage({
        destination: `./public/uploads/${AdminService.MODEL_NAME}`,
        filename: (req, file, callback) => {
          callback(null, FileService.generateFileName(file));
        },
      }),
    })
  )
  async update(
    @Param('id') id: number,
    @UploadedFile() image_path: Express.Multer.File,
    @Body() updateUserAdminDto: UpdateUserAdminDto
  ) {
    // Convert string fields to boolean
    updateUserAdminDto.is_active = ConvertService.stringToBoolean(updateUserAdminDto.is_active);

    updateUserAdminDto.user_type_id = updateUserAdminDto.user_type_id ? Number(updateUserAdminDto.user_type_id) : null;
    updateUserAdminDto.user_role_id = updateUserAdminDto.user_role_id ? Number(updateUserAdminDto.user_role_id) : null;
    updateUserAdminDto.parent_id = updateUserAdminDto.parent_id ? Number(updateUserAdminDto.parent_id) : null;
    updateUserAdminDto.prefix_name_id = updateUserAdminDto.prefix_name_id ? Number(updateUserAdminDto.prefix_name_id) : null;

    // TODO: Wait Hash password match register and login
    if (updateUserAdminDto.password) {
      const hashedPassword = await this.hashPasswordService.hashPassword(updateUserAdminDto.password);
      updateUserAdminDto.password = hashedPassword
    }


    const existingUser = await this.adminService.findOne(Number(id));
    if (image_path) {
      if (existingUser?.image_path) {
        this.fileService.deleteFile(existingUser.image_path);
      }
      updateUserAdminDto.image_path = join(
        "uploads",
        UserService.MODEL_NAME,
        image_path.filename
      );
    } else {
      updateUserAdminDto.image_path = existingUser.image_path;
    }

    const updateDataUser: any = {
      username: updateUserAdminDto.username,
      password: updateUserAdminDto.password,
      phone_number: updateUserAdminDto.phone_number,
      email: updateUserAdminDto.email,
      is_active: updateUserAdminDto.is_active,
      user_role_id: updateUserAdminDto.user_role_id,
      user_type_id: updateUserAdminDto.user_type_id,
      parent_id: updateUserAdminDto.parent_id,
    };

    Object.keys(updateDataUser).forEach((key) => {
      if (updateDataUser[key] === undefined || updateDataUser[key] === null || updateDataUser[key] === "") {
        delete updateDataUser[key];
      }
    });

    const updateDataAdmin: any = {
      user_id: Number(id),
      prefix_name_id: updateUserAdminDto.prefix_name_id,
      first_name: updateUserAdminDto.first_name,
      last_name: updateUserAdminDto.last_name,
      is_active: updateUserAdminDto.is_active,
    };

    Object.keys(updateDataAdmin).forEach((key) => {
      if (updateDataAdmin[key] === undefined || updateDataAdmin[key] === null || updateDataAdmin[key] === "") {
        delete updateDataAdmin[key];
      }
    });

    return await this.userService.updateUserAndAdmin(
      Number(id),
      updateDataUser,
      updateDataAdmin,
      image_path
    );
  }

  @Get()
  @ApiOperation({ summary: "Get a list of all car brands with pagination" })
  @ApiQuery({ name: "page", required: false, type: Number, example: 1 })
  @ApiQuery({ name: "perPage", required: false, type: Number, example: 10 })
  @ApiQuery({ name: "s", required: false, type: String, example: "toyota" })
  @ApiResponse({
    status: 200,
    description: "The list of car brands has been successfully retrieved.",
    type: [User],
  })
  async pagination(
    @Query("page", ParseIntPipe) page: number = 1,
    @Query("perPage", ParseIntPipe) perPage: number = 10,
    @Query("s") s: string = ""
  ) {
    return this.paginationService.paginate(
      UserService.MODEL_NAME,
      page,
      perPage,
      {
        username: {
          contains: s,
        },

      },
      {
        admin: true,
        userType: true,
        userRole: true,
      }
    );
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a single car brand by ID" })
  @ApiResponse({
    status: 200,
    description: "The car brand has been successfully retrieved.",
    type: User,
  })
  findOne(@Param("id", ParseIntPipe) id: number) {
    return this.userService.findOne(id);
  }

  @Get('download/excel')
  async downloadExcel(@Res() res: Response) {
    const header = [
      { header: 'รหัสผู้ใช้งาน', key: 'user_id' },
      { header: 'ชื่อผู้ใช้งาน', key: 'username' },
      { header: 'ชื่อ-นามสกุล', key: 'fullname' },
      { header: 'เบอร์โทรศัพท์', key: 'phone_number' },
      { header: 'อีเมล', key: 'email' },
      { header: 'สิทธิ์ผู้ใช้งาน', key: 'user_role' },
    ];
    let data = [];
    for (const item of (await this.userService.findAll())) {
      data.push({
        user_id: item.id,
        username: item.username,
        fullname: `${item.admin.first_name} ${item.admin.last_name}`,
        phone_number: item.phone_number,
        email: item.email,
        user_role: item.userRole.name,
      });
    }
    await this.excelService.generateExcelFile(header, data, res);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a car brand by ID" })
  @ApiResponse({
    status: 200,
    description: "The car brand has been successfully deleted.",
    type: User,
  })
  remove(@Param("id", ParseIntPipe) id: number) {
    /*  return this.userService.remove(id); */
  }
}
