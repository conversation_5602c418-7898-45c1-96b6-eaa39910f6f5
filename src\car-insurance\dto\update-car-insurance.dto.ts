import { ApiProperty } from '@nestjs/swagger';
import { EnumDriverType } from "@prisma/client";
import { Transform, Type } from "class-transformer";
import {
  IsBoolean,
  IsEnum,
  IsInt,
  IsNumber,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from "class-validator";
import { <PERSON><PERSON><PERSON><PERSON> } from "src/_decorators/transform-decorator.decorator";
import { CreateCarInsuranceCompareDto } from "src/car-insurance-compare/dto/create-car-insurance-compare.dto";
import { CreateCarInsuranceCustomerDto } from "src/car-insurance-customer/dto/create-car-insurance-customer.dto";

export class UpdateCarInsuranceDto {
  @IsInt()
  @ApiProperty({
    example: 1,
    description: "The unique user_employee_id of the car insurance",
    type: Number,
  })
  user_id: number;

  @IsInt()
  @ApiProperty({
    example: 1,
    description: "The unique car_brand_id of the car insurance",
    type: Number,
  })
  car_brand_id: number;

  @IsInt()
  @ApiProperty({
    example: 1,
    description: "The unique car_model_id of the car insurance",
    type: Number,
  })
  car_model_id: number;

  @IsInt()
  @IsOptional()
  @ApiProperty({
    example: 1,
    description: "The unique car_submodel_id of the car insurance",
    type: Number,
    required: false,
  })
  car_submodel_id?: number;

  @IsInt()
  @ApiProperty({
    example: 1,
    description: "The unique insurance_type_id of the car insurance",
    type: Number,
  })
  insurance_type_id: number;

  @IsInt()
  @ApiProperty({
    example: 1,
    description: "The unique license_plate_province_id of the car insurance",
    type: Number,
  })
  license_plate_province_id: number;

  @IsInt()
  @ApiProperty({
    example: 1,
    description: "The unique car_code_id of the car insurance",
    type: Number,
  })
  car_code_id: number;

  @IsInt()
  @ApiProperty({
    example: 1,
    description: "The unique car_insurance_status_id of the car insurance",
    type: Number,
  })
  car_insurance_status_id: number;

  @ValidateIf((obj) => obj.driver_type !== null)
  @IsEnum(EnumDriverType)
  @IsOptional()
  @Transform(({ value }) => value?.toUpperCase())
  @ApiProperty({
    example: "DRIVER",
    description: "DRIVER,NO_DRIVER",
    required: false,
    nullable: true,
  })
  driver_type?: EnumDriverType | null;

  @IsInt()
  @ApiProperty({
    example: 2023,
    description: "The year of the car insurance",
    type: Number,
  })
  year: number;

  @IsNumber({}, { each: true })
  @IsOptional()
  @ApiProperty({
    example: 500000,
    description: "The original insurance value of the car",
    type: Number,
    required: false,
  })
  original_insurance_value?: number;

  @IsNumber({}, { each: true })
  @Transform(({ value }) => Number(value))
  @ApiProperty({
    example: 450000,
    description: "The current insurance value of the car",
    type: Number,
  })
  current_insurance_value: number;

  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "sent",
    description: "The reminder status of the car insurance",
  })
  reminder_status: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "proceed",
    description: "The job status of the car insurance",
  })
  job_status: string;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({
    example: true,
    description: "Indicates whether the car insurance is active",
    type: Boolean,
  })
  is_active?: boolean;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateCarInsuranceCustomerDto)
  @ApiProperty({ type: CreateCarInsuranceCustomerDto, isArray: true })
  car_insurance_customer?: CreateCarInsuranceCustomerDto;

  @IsOptional()
  @ValidateNested({ each: true })
  @ParseJson(CreateCarInsuranceCompareDto)
  @Type(() => CreateCarInsuranceCompareDto)
  @ApiProperty({ type: CreateCarInsuranceCompareDto, isArray: true })
  car_insurance_compares?: CreateCarInsuranceCompareDto[];
}
