import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { CarSubModel } from '../entities/car-sub-model.entity';

export class CreateCarSubModelDto {
  @IsInt()
  @ApiProperty({ example: 1, description: 'The unique car_brand_id of the car sub model', type: Number })
  car_brand_id: number;

  @IsInt()
  @ApiProperty({ example: 1, description: 'The unique car_model_id of the car sub model', type: Number })
  car_model_id: number;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'M0001', description: 'The unique code of the car sub model' })
  code: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'Camry', description: 'The name of the car sub model' })
  name: string;

  @IsInt()
  @IsNotEmpty()
  @ApiProperty({ example: '2019', description: 'The year of the car sub model' })
  year: number;

  @IsBoolean()
  @ApiProperty({ example: true, description: 'The status of the car sub model', type: Boolean })
  is_status: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ example: true, description: 'Indicates whether the car sub model is active', type: Boolean })
  is_active?: boolean;
}