import { ApiProperty } from '@nestjs/swagger';
import { EnumDriverType } from "@prisma/client";
import { Transform } from "class-transformer";
import {
  IsBoolean,
  IsEnum,
  IsInt,
  IsNumber,
  IsOptional,
  IsString,
  ValidateIf,
} from "class-validator";

export class CreateCarInsuranceDto {
  /* @IsInt()
  @ApiProperty({ example: 1, description: 'The unique user_id of the car insurance', type: Number })
  user_id: number; */
  @IsInt()
  @IsOptional()
  @ApiProperty({
    example: "000101",
    description: "The unique quotation_no of car insurance",
    type: String,
  })
  quotation_no?: string;

  @IsInt()
  @IsOptional()
  @ApiProperty({
    example: 1,
    description: "The unique user_employee_id of the car insurance",
    type: Number,
  })
  user_id?: number;

  @IsInt()
  @ApiProperty({
    example: 1,
    description: "The unique car_brand_id of the car insurance",
    type: Number,
  })
  car_brand_id: number;

  @ValidateIf((obj) => obj.driver_type !== null)
  @IsEnum(EnumDriverType)
  @IsOptional()
  @Transform(({ value }) => value?.toUpperCase())
  @ApiProperty({
    example: "DRIVER",
    description: "DRIVER,NO_DRIVER",
    required: false,
    nullable: true,
  })
  driver_type?: EnumDriverType | null;

  @IsInt()
  @ApiProperty({
    example: 1,
    description: "The unique car_model_id of the car insurance",
    type: Number,
  })
  car_model_id: number;

  @IsInt()
  @IsOptional()
  @ApiProperty({
    example: 1,
    description: "The unique car_submodel_id of the car insurance",
    type: Number,
    required: false,
  })
  car_submodel_id?: number;

  @IsInt()
  @ApiProperty({
    example: 1,
    description: "The unique insurance_type_id of the car insurance",
    type: Number,
  })
  insurance_type_id: number;

  @IsInt()
  @ApiProperty({
    example: 1,
    description: "The unique license_plate_province_id of the car insurance",
    type: Number,
  })
  license_plate_province_id: number;

  @IsInt()
  @ApiProperty({
    example: 1,
    description: "The unique car_code_id of the car insurance",
    type: Number,
  })
  car_code_id: number;

  @IsInt()
  @ApiProperty({
    example: 1,
    description: "The unique car_insurance_status_id of the car insurance",
    type: Number,
  })
  car_insurance_status_id: number; //ซื้อได้กับหมดอายุ

  @IsInt()
  @ApiProperty({
    example: 2023,
    description: "The year of the car insurance",
    type: Number,
  })
  year: number;

  @IsNumber({}, { each: true })
  @IsOptional()
  @ApiProperty({
    example: 500000,
    description: "The original insurance value of the car",
    type: Number,
    required: false,
  })
  original_insurance_value?: number;

  @IsNumber({}, { each: true })
  @ApiProperty({
    example: 450000,
    description: "The current insurance value of the car",
    type: Number,
  })
  current_insurance_value: number;

  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "sent",
    description: "The reminder status of the car insurance",
  })
  reminder_status?: string; //อาจจะเป็นสถานะใบเตือน

  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "proceed",
    description: "The job status of the car insurance",
  })
  job_status?: string; //สถานะหน้ากรมธรรม์

  @IsBoolean()
  @IsOptional()
  @ApiProperty({
    example: true,
    description: "Indicates whether the car insurance is active",
    type: Boolean,
  })
  is_active?: boolean;
}
