import { HandleErrorService } from '@common/services/handle-error.services';
import { Injectable } from '@nestjs/common';
import { PermissionAccessPrivilege, Prisma } from '@prisma/client';
import { PrismaService } from 'src/prisma/prisma.service';

@Injectable()
export class PermissionAccessPrivilegeService {
    public static MODEL_NAME = 'permissionAccessPrivilege';
    constructor(
        private prisma: PrismaService,
        private readonly handleErrorService: HandleErrorService
    ) { }

    async findAll(){
        return await this.prisma.permissionAccessPrivilege.findMany();
    }

    /* async create(data: Prisma.CarCodeCreateInput): Promise<PermissionAccessPrivilege> {
        try {
            return await this.prisma.permissionAccessPrivilege.create({
            data,
            });
        } catch (error) {
            this.handleErrorService.handlePrismaError(error);
        }
    } */
}
