
import { CarCodeService } from './car-code.service';
import { UpdateCarCodeDto } from './dto/update-car-code.dto';

import { Controller, Get, Post, Body, Put, Param, Delete, Query, ParseIntPipe, UseGuards } from '@nestjs/common';

import { ApiBearerAuth, ApiOkResponse } from '@nestjs/swagger';
import { CarCode } from './entities/car-code.entity';
import { Prisma } from '@prisma/client';
import { PaginationService } from "@common/services/pagination.service";
import { CreateCarCodeDto } from "./dto/create-car-code.dto";
import { ConvertService } from "@common/services/convert.service";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
@ApiTags("car-code")

@Controller('car-code')
export class CarCodeController {
  constructor(
    private readonly CarCodeService: CarCodeService,
    private readonly paginationService: PaginationService,
  ) { }

  @Get("paginate")
  @ApiOperation({ summary: "Get a list of all car code with pagination" })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'perPage', required: false, type: Number, example: 10 })
  @ApiQuery({ name: 'searchText', required: false, type: String, example: 'รถยนต์ส่วนบุคคล' })
  @ApiResponse({
    status: 200,
    description: "The list of car code has been successfully retrieved.",
    type: [CarCode],
  })
  async pagination(
    @Query('page', ParseIntPipe) page: number = 1,
    @Query('perPage', ParseIntPipe) perPage: number = 10,
    @Query('searchText') searchText: string = ""
  ) {
    return this.paginationService.paginate(CarCodeService.Model, page, perPage, {
      OR: [
          {name: { contains: searchText.toLowerCase() }},
          {code: { contains: searchText.toLowerCase() }}
        ]
    });
  }

  @Post()
  @ApiOperation({ summary: "Create a new car code" })
  @ApiConsumes('application/json')
  @ApiResponse({
    status: 201,
    description: "The car code has been successfully created.",
    type: CreateCarCodeDto,
  })
  async create(@Body() createCarCodeDto: CreateCarCodeDto) {
    /* createCarCodeDto.is_status = ConvertService.stringToBoolean(
      createCarCodeDto.is_status
    ); */
    return this.CarCodeService.create(createCarCodeDto);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update a  car code by ID" })
  @ApiConsumes('application/json')
  @ApiResponse({
    status: 200,
    description: "The  car code has been successfully updated.",
    type: UpdateCarCodeDto,
  })

  async update(
    @Param("id", ParseIntPipe) id: number,
    @Body() updateCarCodeDto: UpdateCarCodeDto
  ) {
    updateCarCodeDto.is_active = ConvertService.stringToBoolean(
      updateCarCodeDto.is_active
    );
    return this.CarCodeService.update(id, updateCarCodeDto);
  }

  @Get()
  @ApiOperation({ summary: "Get insurance type model All" })
  @ApiResponse({
    status: 200,
    description: "The insurance type has been successfully retrieved.",
    type: CarCode,
  })
  findAll() {
    return this.CarCodeService.findAll();
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a single car code by ID" })
  @ApiResponse({
    status: 200,
    description: "The car code has been successfully retrieved.",
    type: CarCode,
  })
  findOne(@Param("id", ParseIntPipe) id: number) {
    return this.CarCodeService.findOne(id);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a  car code by ID" })
  @ApiResponse({
    status: 200,
    description: "The car code has been successfully deleted.",
    type: CarCode,
  })
  remove(@Param("id", ParseIntPipe) id: number) {
    return this.CarCodeService.remove(id);
  }
}
