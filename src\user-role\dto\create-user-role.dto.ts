import { IsBoolean, IsString, <PERSON><PERSON>otEmpty, IsArray, <PERSON>idateNested ,IsInt} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

import { CreateUserRolePermissionDto } from "../../user-role-permission/dto/create-user-role-permission.dto";

export class CreateUserRoleDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    example: "test",
    description: '',
  })
  code: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    example: "test",
    description: '',
  })
  name: string;

  @IsBoolean()
  @ApiProperty({
    example: true,
    description: '',
  })
  is_active: boolean;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateUserRolePermissionDto)
  @ApiProperty({
    type: [CreateUserRolePermissionDto],
    description: 'List of permissions associated with the user role.',
    example: [
      {
        permission_id: 1,
        is_granted: true,
      },
      {
        permission_id: 2,
        is_granted: true,
      },
    ],
  })
  user_role_permission: CreateUserRolePermissionDto[];
}