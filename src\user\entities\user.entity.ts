import { IsObject } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';
import { Admin } from '../../admin/entities/admin.entity'
import { UserRole } from 'src/user-role/entities/user-role.entity';
export class User {
  @ApiProperty({
    example: 1,
    description: 'The unique identifier of the car brand',
  })
  id: number;

  @ApiProperty({ example: 1, description: 'The role ID of the user' })
  user_role_id: number;

  @ApiProperty({ example: 2, description: 'The type ID of the user' })
  user_type_id: number;

  @ApiProperty({ example: 3, description: 'The parent ID of the user (if any)', required: false })
  parent_id?: number;

  @ApiProperty({ example: 'john_doe', description: 'The username of the user' })
  username: string;

  @ApiProperty({ example: 'password123', description: 'The password of the user' })
  password: string;

  @ApiProperty({ example: '0801234567', description: 'The phone number of the user', required: false })
  phone_number?: string;

  @ApiProperty({ example: '<EMAIL>', description: 'The email address of the user' })
  email: string;

  @ApiProperty({ example: true, description: 'The status of the user', default: true, required: false })
  is_status?: boolean = true;

  @ApiProperty({ example: true, description: 'Indicates if the user is active', default: true, required: false })
  is_active?: boolean = true;

  admin?: any = Admin;
  userRole?: any = UserRole;

}
