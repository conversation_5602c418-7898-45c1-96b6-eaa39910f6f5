import { Module } from '@nestjs/common';
import { ConfigModule } from "@nestjs/config";
import { APP_INTERCEPTOR } from "@nestjs/core";
import { CarBrandModule } from "src/car-brand/car-brand.module";
import { TransformResponseInterceptor } from "./_intercepters/response-interceptor/response-interceptor.interceptor";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { CarCodeModule } from "./car-code/car-code.module";
import { CarInsuranceModule } from "./car-insurance/car-insurance.module";
import { CarModelModule } from "./car-model/car-model.module";
import { CarSubModelModule } from "./car-sub-model/car-sub-model.module";
import { ColorModule } from "./color/color.module";
import { InsuranceTypeModule } from "./insurance-type/insurance-type.module";
import { LicensePlateProvinceModule } from "./license-plate-province/license-plate-province.module";
import { PermissionModule } from "./permission/permission.module";
import { PlaceModule } from "./place/place.module";
import { TestTableModule } from "./test-table/test-table.module";
import { UserRolePermissionModule } from "./user-role-permission/user-role-permission.module";
import { UserRoleModule } from "./user-role/user-role.module";
import { UserModule } from "./user/user.module";
import { YearModule } from "./year/year.module";

import { ServeStaticModule } from "@nestjs/serve-static";
import { join } from "path";
import { AttachdocumentsModule } from "./attachdocuments/attachdocuments.module";
import { AuthModule } from "./auth/auth.module";
import { BusinessTypeModule } from "./business-type/business-type.module";
import { CarInsuranceCompareAddonsModule } from "./car-insurance-compare-addons/car-insurance-compare-addons.module";
import { CarInsuranceCompareModule } from "./car-insurance-compare/car-insurance-compare.module";
import { CarInsuranceCustomerModule } from "./car-insurance-customer/car-insurance-customer.module";
import { CustomerModule } from "./customer/customer.module";
import { HwcFileUploadModule } from "./hwc-file-upload/hwc-file-upload.module";
import { InsuranceAddonServiceModule } from "./insurance-addon-service/insurance-addon-service.module";
import { InsuranceModule } from "./insurance/insurance.module";
import { MstAccessPrivilegeModule } from "./mst-access-privilege/mst-access-privilege.module";
import { PermissionAccessPrivilegeModule } from "./permission-access-privilege/permission-access-privilege.module";
import { PrefixNameModule } from "./prefix-name/prefix-name.module";
import { PrismaModule } from "./prisma/prisma.module";

import { PaymentModule } from "./payment/payment.module";
import { VendorModule } from "./vendor/vendor.module";

@Module({
  imports: [
    PrismaModule,
    TestTableModule,
    CarBrandModule,
    InsuranceTypeModule,
    PlaceModule,
    CarSubModelModule,
    CarModelModule,
    PermissionModule,
    UserRolePermissionModule,
    UserRoleModule,
    ColorModule,
    CarCodeModule,
    UserModule,
    YearModule,
    CarInsuranceModule,
    LicensePlateProvinceModule,
    InsuranceModule,
    InsuranceAddonServiceModule,
    HwcFileUploadModule,

    // Vendor
    VendorModule,
    ConfigModule.forRoot(),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, "..", "..", "public"),
      // serveRoot: '/public/',
      serveStaticOptions: { index: false },
    }),
    MstAccessPrivilegeModule,
    PermissionAccessPrivilegeModule,

    PrefixNameModule,
    CarInsuranceCustomerModule,
    BusinessTypeModule,
    CarInsuranceCompareModule,
    CarInsuranceCompareAddonsModule,
    AuthModule,
    CustomerModule,
    AttachdocumentsModule,
    PaymentModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: TransformResponseInterceptor,
    },
  ],
})
export class AppModule {}
