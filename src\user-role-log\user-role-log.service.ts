import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
// import { UserRoleLog } from './entities/car-model.entity';
import { UserRoleLog } from '@prisma/client';
import { Prisma } from '@prisma/client';
import { join } from 'path';
import { CreateUserRoleLogDto } from './dto/create-user-role-log.dto';
import { HandleErrorService } from "@common/services/handle-error.services";

@Injectable()
export class UserRoleLogService {
  public static MODEL_NAME = 'userRoleLog';
  constructor(private prisma: PrismaService,
    private readonly handleErrorService: HandleErrorService) { }

  async create(prisma:any,data: CreateUserRoleLogDto): Promise<UserRoleLog> {
    try {
      return await prisma.userRoleLog.create({
        data
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }

  }

}
