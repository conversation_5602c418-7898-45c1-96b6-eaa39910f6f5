import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreatePermissionAccessPriVilegeDto {
  @IsInt()
  @IsOptional()
  @ApiProperty({ example: 1, description: 'The unique permission_id of the permission access privilege ', type: Number })
  permission_id?: number;

  @IsInt()
  @ApiProperty({ example: 1, description: 'The unique mst_access_privilege_id of the permission access privilege', type: Number })
  mst_access_privilege_id: number;

  @IsBoolean()
  @ApiProperty({ example: true, description: 'The status of the permission access privilege', type: Boolean })
  is_allow: boolean;

}