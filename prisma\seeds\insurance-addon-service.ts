import { PrismaClient } from "@prisma/client";
const prisma = new PrismaClient();

const seedData = [
    { id: 1, code:"INAS001", name: "ช่วยเหลือฉุกเฉิน" },
    { id: 2, code:"INAS002", name: "ภัยธรรมชาติ" },
    { id: 3, code:"INAS003", name: "ภัยน้ำท่วม" },
    { id: 4, code:"INAS004", name: "รถใช้ระหว่างซ่อม" },
];

export async function insuranceAddonSeviceSeeder() {
    await Promise.all(
        seedData.map(async (data) =>
        prisma.insuranceAddonService.upsert({
            where: { id: data.id },
            update: { name: data.name },
            create: data,
        })
        )
    );

    console.log('insuranceAddonSeviceSeeder-Success')
}
