import { PrismaClient } from "@prisma/client";
const prisma = new PrismaClient();

const seedData = [
    { id: 1, name: "นาย" },
    { id: 2, name: "นาง" },
    { id: 3, name: "นางสาว"},
    { id: 4, name: "ไม่ระบุ"}
];

export async function perfixNameSeeder() {
    await Promise.all(
        seedData.map(async (data) =>
        prisma.prefixName.upsert({
            where: { id: data.id },
            update: { name: data.name },
            create: data,
        })
        )
    );

    console.log('prefixNameSeeder-Success')
}
