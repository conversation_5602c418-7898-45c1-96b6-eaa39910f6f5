// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["prismaSchemaFolder"]
}

// model TestTable {
//   id         Int      @id @default(autoincrement())
//   name       String
//   is_active  Boolean  @default(true)
//   created_at DateTime @default(now())
//   updated_at DateTime @updatedAt

//   @@map("test_table")
// }
model Agent {
  id                             Int      @id @default(autoincrement())
  user_id                        Int   
  code                           String   @unique @db.VarChar(50)
  image_path                     String?
  fullname                       String
  phone_number                   String   @db.VarChar(20)
  password                       String
  email                          String   @unique
  contact_email                  String?
  is_insurance_broker_license    Boolean
  referral_code                  String?  @db.VarChar(50)
  agent_type_id                  Int
  occupation_id                  Int
  insurance_sales_experience_id  Int
  birthdate                      DateTime? @db.Date
  gender                         String?  @db.VarChar(10)
  id_card_house_number           String?  @db.VarChar(50)
  id_card_address                String?
  id_card_sub_district         String?
    id_card_district         String?
        id_card_province         String?
  id_card_zip_code               String?  @db.VarChar(10)
  document_delivery_status       String?  @db.VarChar(50)
  delivery_house_number          String?  @db.VarChar(50)
  delivery_address               String?
  delivery_sub_district         String?
    delivery_district         String?
        delivery_province         String?
  delivery_zip_code              String?  @db.VarChar(10)
  card_number                    String   @unique @db.VarChar(20)
  id_card_image_path             String?
  broker_license_number          String   @db.VarChar(50)
  broker_license_expiry_date     DateTime? @db.Date
  broker_license_image_path      String?
  bank_name                      String   @db.VarChar(100)
  branch_code                    String   @db.VarChar(50)
  bank_account_number            String   @unique @db.VarChar(100)
  is_active                      Boolean  @default(true)
  created_at                     DateTime @default(now())
  updated_at                     DateTime @updatedAt
  user                User   @relation(fields: [user_id], references: [id])
  agent_type          AgentType @relation(fields: [agent_type_id], references: [id])
  occupation          Occupation @relation(fields: [occupation_id], references: [id])
  insurance_sales_experience     InsuranceSalesExperience @relation(fields: [insurance_sales_experience_id], references: [id])

  @@map("agent")
}

model AgentType {
  id             Int      @id @default(autoincrement())
  name           String   @db.VarChar(255)
  is_status      Boolean  @default(true)
  is_active      Boolean  @default(true)
  created_at     DateTime @default(now())
  updated_at     DateTime @updatedAt

  agent Agent[]

  @@map("agent_type")
}

model BankAccount {
  id                Int       @id @default(autoincrement())
  payment_id        Int
  bank_logo         String?
  bank_name         String?   @db.VarChar(100)
  branch_name       String?   @db.VarChar(100)
  account_number    String    @db.VarChar(50)
  account_name      String    @db.VarChar(100)
  is_active         Boolean   @default(true)
  created_at        DateTime  @default(now())
  updated_at        DateTime  @updatedAt

  payment       Payment   @relation(fields: [payment_id], references: [id])

  @@map("bank_account")
}

model BusinessType {
  id                Int       @id @default(autoincrement())
  name_th           String?
  name_en           String?
  prefix_name_th    String?
  prefix_name_en    String?
  after_name_th     String?
  after_name_en     String?
  is_active         Boolean   @default(true)
  created_at        DateTime  @default(now())
  updated_at        DateTime  @updatedAt

  carInsuranceCustomer  CarInsuranceCustomer[]
  customer                   Customer[]

  @@map("business_type")
}

model CarAct {
  id                      Int       @id @default(autoincrement())
  user_id                 Int
  insurance_id            Int
  car_code_id             Int
  payment_id              Int?
  car_registration_number String    @db.VarChar(50)
  expiration_date         DateTime  @db.Date
  is_active               Boolean   @default(true)
  created_at              DateTime  @default(now())
  updated_at              DateTime  @updatedAt

  user                User      @relation(fields: [user_id], references: [id])   
  insurance           Insurance @relation(fields: [insurance_id], references: [id])
  carCode             CarCode   @relation(fields: [car_code_id], references: [id])
  payment             Payment?  @relation(fields: [payment_id], references: [id])
  
  carActPayment             CarActPayment[]
  carActPolicyDetail        CarActPolicyDetail[]
  carActPolicyPageDetail    CarActPolicyPageDetail[]
  
  @@map("car_act")
}

model CarActPayment {
  id                Int       @id @default(autoincrement())
  car_act_id        Int
  payment_id        Int?
  is_active         Boolean   @default(true)
  created_at        DateTime  @default(now())
  updated_at        DateTime  @updatedAt

  carAct      CarAct    @relation(fields: [car_act_id], references: [id])
  payment     Payment?  @relation(fields: [payment_id], references: [id])
  
  @@map("car_act_payment")
}

model CarActPolicyDetail {
  id                            Int       @id @default(autoincrement())
  car_act_id                    Int
  license_plate_province_id     Int
  car_brand_id                  Int
  car_model_id                  Int
  coverage_start_date           DateTime  @db.Date
  coverage_end_date             DateTime  @db.Date
  chassis_number                String    @db.VarChar(100)
  year                          Int
  cc                            Int
  weight                        Decimal?  @db.Decimal(10,2)
  color_id                      Int
  is_active                     Boolean   @default(true)
  created_at                    DateTime  @default(now())
  updated_at                    DateTime  @updatedAt

  carAct                CarAct                  @relation(fields: [car_act_id], references: [id])
  licensePlateProvince  LicensePlateProvince    @relation(fields: [license_plate_province_id], references: [id])
  carBrand              CarBrand                @relation(fields: [car_brand_id], references: [id])
  carModel              CarModel                @relation(fields: [car_model_id], references: [id])
  color                 Color                   @relation(fields: [color_id], references: [id])

  @@map("car_act_policy_detail")
}

model CarActPolicyPageDetail {
  id                        Int         @id @default(autoincrement())
  car_act_id                Int
  province_id               Int
  prefix_id                 Int
  id_card_type              String      @db.VarChar(50)
  id_card_number            String      @db.VarChar(20)
  first_name                String      @db.VarChar(100)
  last_name                 String      @db.VarChar(100)
  gender                    String      @db.VarChar(10)
  phone_number              String      @db.VarChar(20)
  nationality               String      @db.VarChar(50)
  house_number              String?     @db.VarChar(50)
  address                   String?
  sub_district              String      @db.VarChar(100)
  district                  String      @db.VarChar(100)
  zip_code                  String      @db.VarChar(10)
  is_active                 Boolean     @default(true)
  created_at                DateTime    @default(now())
  updated_at                DateTime    @updatedAt

  carAct                CarAct                  @relation(fields: [car_act_id], references: [id])
  province              Province                @relation(fields: [province_id], references: [id])
  prefixName            PrefixName              @relation(fields: [prefix_id], references: [id])     

  @@map("car_act_policy_page_detail")
}

model CarInsuranceAddons {
  id                          Int         @id @default(autoincrement())
  car_insurance_id            Int
  insurance_addon_service_id  Int?    
  is_active                   Boolean     @default(true)
  created_at                  DateTime    @default(now())
  updated_at                  DateTime    @updatedAt

  carInsurance                    CarInsurance                @relation(fields: [car_insurance_id], references: [id])
  insuranceAddonService           InsuranceAddonService?      @relation(fields: [insurance_addon_service_id], references: [id])
  
  @@map("car_insurance_addons")
}

model CarInsuranceCompare {
  id                                Int         @id @default(autoincrement())
  car_insurance_id                  Int
  insurance_id                      Int
  insurance_type_id                 Int
  is_named_driver                   Boolean      @default(true)
  is_excess                         Boolean      @default(true)
  price                             Decimal?     @db.Decimal(15,2)
  is_prb                            Boolean      @default(false)
  prb_price                         Decimal?     @db.Decimal(15,2)
  is_discount                       Boolean      @default(false)
  agent_discount                    Decimal?     @db.Decimal(15,2)
  discount_price                    Decimal?     @db.Decimal(15,2)
  vehicle_damage                    Decimal?     @db.Decimal(15,2)
  vehicle_loss_or_fire              Decimal?     @db.Decimal(15,2)
  external_person_life_per_person   Decimal?     @db.Decimal(15,2)
  external_person_life_per_case     Decimal?     @db.Decimal(15,2)
  external_person_property          Decimal?     @db.Decimal(15,2)
  excess_property_damage            Decimal?     @db.Decimal(15,2)
  personal_accident                 Decimal?     @db.Decimal(15,2)
  medical_expense_per_person        Decimal?     @db.Decimal(15,2)
  driver_bail                       Decimal?     @db.Decimal(15,2)
  current_insurance_value           Decimal?     @db.Decimal(15,2)
  marketing_cost                    Decimal?     @db.Decimal(15,2)
  excess                            Decimal?     @db.Decimal(15,2)
  is_active                         Boolean      @default(true)
  created_at                        DateTime     @default(now())
  updated_at                        DateTime     @updatedAt

  carInsurance                CarInsurance        @relation(fields: [car_insurance_id], references: [id])
  insurance                   Insurance           @relation(fields: [insurance_id], references: [id])
  insuranceType               InsuranceType       @relation(fields: [insurance_type_id], references: [id])

  carInsuranceCompareAddons   CarInsuranceCompareAddons[]
  // carInsuranceCompareDetail   CarInsuranceCompareDetail[]
  
  @@map("car_insurance_compare")
}

model CarInsuranceCompareAddons {
  id                          Int          @id @default(autoincrement())
  car_insurance_compare_id    Int
  insurance_addon_service_id  Int?
  is_active                   Boolean      @default(true)
  created_at                  DateTime     @default(now())
  updated_at                  DateTime     @updatedAt

  carInsuranceCompare             CarInsuranceCompare         @relation(fields: [car_insurance_compare_id], references: [id],onDelete: Cascade)
  insuranceAddonService           InsuranceAddonService?      @relation(fields: [insurance_addon_service_id], references: [id])
  
  @@map("car_insurance_compare_addons")
}

// model CarInsuranceCompareDetail {
//   id                                Int          @id @default(autoincrement())
//   insurance_id                      Int
//   insurance_type_id                 Int
//   car_insurance_compare_id          Int
//   is_named_driver                   Boolean      @default(true)
//   is_excess                         Boolean      @default(true)
//   price                             Decimal?     @db.Decimal(15,2)
//   vehicle_damage                    Decimal?     @db.Decimal(15,2)
//   vehicle_loss_or_fire              Decimal?     @db.Decimal(15,2)
//   external_person_life_per_person   Decimal?     @db.Decimal(15,2)
//   external_person_life_per_case     Decimal?     @db.Decimal(15,2)
//   external_person_property          Decimal?     @db.Decimal(15,2)
//   excess_property_damage            Decimal?     @db.Decimal(15,2)
//   personal_accident                 Decimal?     @db.Decimal(15,2)
//   medical_expense_per_person        Decimal?     @db.Decimal(15,2)
//   driver_bail                       Decimal?     @db.Decimal(15,2)
//   current_insurance_value           Decimal?     @db.Decimal(15,2)
//   marketing_cost                    Decimal?     @db.Decimal(15,2)
//   excess                            Decimal?     @db.Decimal(15,2)
//   is_active                         Boolean      @default(true)
//   created_at                        DateTime     @default(now())
//   updated_at                        DateTime     @updatedAt

//   insurance                   Insurance           @relation(fields: [insurance_id], references: [id])
//   insuranceType               InsuranceType       @relation(fields: [insurance_type_id], references: [id])
//   carInsuranceCompare         CarInsuranceCompare @relation(fields: [car_insurance_compare_id], references: [id])
  
//   @@map("car_insurance_compare_detail")
// }

model CarInsuranceCustomer {
  id                                Int           @id @default(autoincrement())
  car_insurance_id                  Int 
  registration_type_id              Int
  prefix_name_id                    Int?
  license_plate_province_id         Int
  business_type_id                  Int
  phone_number                      String        @db.VarChar(20)
  first_name                        String?
  last_name                         String?
  business_name                     String?
  address                           String?       @db.Text
  car_registration_number           String        @db.VarChar(50)
  chassis_no     String?
  engine_no     String?
  coverage_start_date     DateTime?
  act_coverage_date     DateTime?
  driver_type     EnumDriverType?
  driver_name_1     String?
  brith_date_driver_1     DateTime?
  driver_name_2     String?
  brith_date_driver_2     DateTime?
  beneficiary_type     String?
  beneficiary_prefix_name_id     Int?
  beneficiary_first_name     String?
  beneficiary_last_name     String?
  id_card_type     Int
  id_card_no     String?
  passport_no     String?
  bussiness_name     String?
  birth_date     DateTime?
  policy_house_no     String?
  sub_district     String?
  district     String?
  province     String?
  zip_code     String?
  
  id_passport_file                  String?
  car_regis_book_file               String?
  og_insur_policy_file              String?
  agree         Boolean       @default(false)
  vehicle_fit_certi                 String?      
  is_active                         Boolean       @default(true)
  created_at                        DateTime      @default(now())
  updated_at                        DateTime      @updatedAt

  carInsurance              CarInsurance              @relation(fields: [car_insurance_id], references: [id])
  registrationType          RegistrationType          @relation(fields: [registration_type_id], references: [id])
  prefixName                PrefixName?               @relation(fields: [prefix_name_id], references: [id])
  licensePlateProvince      LicensePlateProvince      @relation(fields: [license_plate_province_id], references: [id])
  businessType              BusinessType              @relation(fields: [business_type_id], references: [id])
  carCustomerImage          CarCustomerImage[]

  @@map("car_insurance_customer")
}

model CarInsuranceDetail {
  /// Primary key, auto-Increment
  id                                Int          @id  @default(autoincrement())
  insurance_id                      Int        
  insurance_type_id                 Int
  car_insurance_id                  Int
  is_named_driver                   Boolean      @default(false)
  is_excess                         Boolean      @default(false)
  price                             Decimal?     @db.Decimal(15,2)
  vehicle_damage                    Decimal?     @db.Decimal(15,2)
  vehicle_loss_or_fire              Decimal?     @db.Decimal(15,2)
  external_person_life_per_person   Decimal?     @db.Decimal(15,2)
  external_person_life_per_case     Decimal?     @db.Decimal(15,2)
  external_person_property          Decimal?     @db.Decimal(15,2)
  excess_property_damage            Decimal?     @db.Decimal(15,2)
  personal_accident                 Decimal?     @db.Decimal(15,2)
  medical_expense_per_person        Decimal?     @db.Decimal(15,2)
  driver_bail                       Decimal?     @db.Decimal(15,2)
  current_insurance_value           Decimal?     @db.Decimal(15,2)
  marketing_cost                    Decimal?     @db.Decimal(15,2)
  excess                            Decimal?     @db.Decimal(15,2)
  is_active                         Boolean      @default(true)
  created_at                        DateTime     @default(now())
  updated_at                        DateTime     @updatedAt

  insurance           Insurance               @relation(fields: [insurance_id], references: [id])
  insuranceType       InsuranceType           @relation(fields: [insurance_type_id], references: [id])
  carInsurance        CarInsurance            @relation(fields: [car_insurance_id], references: [id])

  @@map("car_insurance_detail")
}

model CarInsurancePayment {
  id                                Int             @id @default(autoincrement())
  car_insurance_id                  Int
  payment_id                        Int?
  is_active                         Boolean         @default(true)
  created_at                        DateTime        @default(now())
  updated_at                        DateTime        @updatedAt

  carInsurance            CarInsurance          @relation(fields: [car_insurance_id], references: [id])
  payment                 Payment?         @relation(fields: [payment_id], references: [id])
  
  @@map("car_insurance_payment")
}

model DeejingCredit {
  id                              Int                 @id @default(autoincrement())
  user_id                         Int
  balance                         Decimal             @db.Decimal(15,2)@default(0.00)
  status                          String              @db.VarChar(50)@default("active")
  is_active                       Boolean             @default(true)
  created_at                      DateTime            @default(now())
  updated_at                      DateTime            @updatedAt

  user                        User                @relation(fields: [user_id], references: [id])
  deejingCreditTransactionLog       DeejingCreditTransactionLog[]
  payment                           Payment[]

  @@map("deejing_credit")
}

model DeejingCreditTransactionLog {
  id                              Int                 @id @default(autoincrement())
  deejing_credit_id               Int
  transaction_type                String              @db.VarChar(50)
  amount                          Decimal             @db.Decimal(15,2)
  transaction_reference           String?
  previous_balance                Decimal             @db.Decimal(15,2)
  new_balance                     Decimal             @db.Decimal(15,2)
  description                     String?             @db.Text
  is_active                       Boolean             @default(true)
  created_at                      DateTime            @default(now())
  updated_at                      DateTime            @updatedAt

  deejingCredit             DeejingCredit           @relation(fields: [deejing_credit_id], references: [id])

  @@map("deejing_credit_transaction_log")
}

model District {
  id                           Int              @id @default(autoincrement())
  code                         String           @unique @db.VarChar(50)
  name_th                      String
  name_en                      String?
  province_id                  Int
  is_active                    Boolean          @default(true)
  created_at                   DateTime         @default(now())
  updated_at                   DateTime         @updatedAt

  province                     Province         @relation(fields: [province_id], references: [id])

  @@map("district")
}

model InsuranceSalesExperience {
  id                          Int               @id @default(autoincrement())
  name                        String
  is_active                   Boolean           @default(true)
  created_at                  DateTime          @default(now())
  updated_at                  DateTime          @updatedAt

  agent                 Agent[]

  @@map("insurance_sales_experience")
}

model MarketingFeeRateAct {
  id                              Int               @id @default(autoincrement())
  user_id                         Int
  car_code_id                     Int
  insurance_id                    Int
  marketing_fee_rate_act_type_id  Int
  percent                         Decimal           @db.Decimal(5,2)
  is_active                       Boolean           @default(true)
  created_at                      DateTime          @default(now())
  updated_at                      DateTime          @updatedAt

  user                    User                    @relation(fields: [user_id], references: [id])
  carCode                 CarCode                 @relation(fields: [car_code_id], references: [id])
  insurance               Insurance               @relation(fields: [insurance_id], references: [id])
  marketingFeeRateActType MarketingFeeRateActType @relation(fields: [marketing_fee_rate_act_type_id], references: [id])

  @@map("marketing_fee_rate_act")
}

model MarketingFeeRateActType {
  id                            Int                 @id @default(autoincrement())
  name                          String              @db.VarChar(50)
  is_active                     Boolean             @default(true)
  created_at                    DateTime            @default(now())
  updated_at                    DateTime            @updatedAt

  marketingFeeRateAct       MarketingFeeRateAct[]

  @@map("marketing_fee_rate_act_type")
}

model MarketingFeeRateInsurance {
  id                            Int                 @id @default(autoincrement())
  user_id                       Int
  insurance_type_id             Int
  insurance_id                  Int
  percent                       Decimal             @db.Decimal(5,2)
  is_active                     Boolean             @default(true)
  created_at                    DateTime            @default(now())
  updated_at                    DateTime            @updatedAt

  user            User            @relation(fields: [user_id], references: [id])
  insuranceType   InsuranceType   @relation(fields: [insurance_type_id], references: [id])
  insurance       Insurance       @relation(fields: [insurance_id], references: [id])

  @@map("marketing_fee_rate_insurance")
}

model MwsCmiVehicleType {
  id                            Int                 @id @default(autoincrement())
  cmi_veh_type_code             String              @db.VarChar(50)
  oic_description_th            String
  tsri_description_th           String
  tsri_description_en           String
  scale_type                    String              @db.VarChar(2)  ///(S-Seat, W-WG, C-CC, K-KW)
  capacity_min                  Int
  capacity_max                  Int
  code_type                     String              @db.VarChar(2)  ///(M-Main, S-Sub, E-EV)
  node                          String              @db.VarChar(50)
  usage_type                    String              @db.VarChar(2)  ///(P-Personal, C-Commercial, R-Public/Rental)	
  usage_code                    String              @db.VarChar(50)
  body_type                     String              @db.VarChar(50)
  body_name_en                  String
  is_active                     Boolean             @default(true)
  created_at                    DateTime            @default(now())
  updated_at                    DateTime            @updatedAt

  @@map("mws_cmi_vehicle_type")
}

model MwsPrefix {
  id                            Int                 @id @default(autoincrement())
  registration_type_id          Int
  insured_type                  String
  title_code                    String
  description_th                String
  description_en                String
  is_active                     Boolean             @default(true)
  created_at                    DateTime            @default(now())
  updated_at                    DateTime            @updatedAt

  registrationType              RegistrationType    @relation(fields: [registration_type_id], references: [id])

  @@map("mws_prefix")
}

model MwsVmiVehicleType {
  id                            Int                 @id @default(autoincrement())
  vehicle_code                  String              @unique @db.VarChar(50)     
  vehicle_type_th               String              @db.VarChar(50)
  vehicle_type_en               String              @db.VarChar(50)
  usage_code                    String              @db.VarChar(50)
  is_active                     Boolean             @default(true)
  created_at                    DateTime            @default(now())
  updated_at                    DateTime            @updatedAt

  @@map("mws_vmi_vehicle_type")
}

model MwsVmiVehicleUsage {
  id                            Int                 @id @default(autoincrement())
  usage_code                    String              @db.VarChar(50)
  usage_th                      String              @db.VarChar(50)
  usage_en                      String              @db.VarChar(50)
  is_active                     Boolean             @default(true)
  created_at                    DateTime            @default(now())
  updated_at                    DateTime            @updatedAt

  @@map("mws_vmi_vehicle_usage")
}

model Occupation {
  id                            Int                 @id @default(autoincrement())
  name                          String
  is_active                     Boolean             @default(true)
  created_at                    DateTime            @default(now())
  updated_at                    DateTime            @updatedAt

  agent   Agent[]

  @@map("occupation")
}

model Payment {
  id                             Int                @id @default(autoincrement())
  user_id                        Int
  payment_method_id              Int      
  deejing_credit_id              Int?
  payment_reference              String             @unique
  amount                         Decimal            @db.Decimal(15,2)
  payment_status                 String             @db.VarChar(50)
  payment_date                   DateTime           @db.Date
  payment_time                   DateTime           @db.Time
  payment_proof_path             String?             
  is_active                      Boolean            @default(true)
  created_at                     DateTime           @default(now())
  updated_at                     DateTime           @updatedAt

  user                User                @relation(fields: [user_id], references: [id])
  paymentMethod       PaymentMethod       @relation(fields: [payment_method_id], references: [id])
  deejinCredit        DeejingCredit?      @relation(fields: [deejing_credit_id], references: [id])
  bankAccount         BankAccount[]
  carAct              CarAct[]
  carActPayment       CarActPayment[]
  carInsurancePayment CarInsurancePayment[]

  @@map("payment")
}

model PaymentMethod {
  id                            Int                 @id @default(autoincrement())
  name                          String
  is_active                     Boolean             @default(true)
  created_at                    DateTime            @default(now())
  updated_at                    DateTime            @updatedAt

  payment     Payment[]

  @@map("payment_method")
}

model Province {
  id                            Int                 @id @default(autoincrement())
  code                          String              @unique @db.VarChar(50)
  name_th                       String
  name_en                       String
  is_active                     Boolean             @default(true)
  created_at                    DateTime            @default(now())
  updated_at                    DateTime            @updatedAt

  carActPolicyPageDetail     CarActPolicyPageDetail[]
  // carInsuranceCustomer       CarInsuranceCustomer[]
  district                   District[]

  @@map("province")
}

model RegistrationType {
  id                            Int                 @id @default(autoincrement())
  code                          String              @unique @db.VarChar(50)
  name                          String
  is_active                     Boolean             @default(true)
  created_at                    DateTime            @default(now())
  updated_at                    DateTime            @updatedAt

  carInsuranceCustomer       CarInsuranceCustomer[]
  mwsPrefix                  MwsPrefix[]
  customer                   Customer[]
  @@map("registration_type")
}

model Subagent {
  id                            Int                 @id @default(autoincrement())
  user_id                       Int
  code                          String              @unique @db.VarChar(50)
  full_name                     String
  phone_number                  String              @db.VarChar(20)
  password                      String
  email                         String              @unique
  card_number                   String?             @unique @db.VarChar(13)
  birthdate                     DateTime?           @db.Date
  gender                        String?             @db.VarChar(10)
  address                       String?
  sub_district_id               Int
  zip_code                      String?             @db.VarChar(10)
  is_active                     Boolean             @default(true)
  created_at                    DateTime            @default(now())
  updated_at                    DateTime            @updatedAt

  user                      User                  @relation(fields: [user_id], references: [id])

  @@map("subagent")
}



// model Vender {
//   id                            Int                 @id @default(autoincrement())
//   code                          String              @unique @db.VarChar(50)
//   name                          String
//   is_active                     Boolean             @default(true)
//   created_at                    DateTime            @default(now())
//   updated_at                    DateTime            @updatedAt

//   venderMapping     VenderMapping[]

//   @@map("vender")
// }

// model VenderMapping {
//   id                            Int                 @id @default(autoincrement())
//   vender_id                     Int
//   model                         String              @unique
//   table_mapping                 String
//   is_active                     Boolean             @default(true)
//   created_at                    DateTime            @default(now())
//   updated_at                    DateTime            @updatedAt

//   vender        Vender      @relation(fields: [vender_id], references: [id])

//   @@map("vender_mapping")
// }

// New Table
model MstAccessPrivilege {
  id                            Int                 @id @default(autoincrement())
  name                          String
  is_status      Boolean  @default(true)
  is_active                     Boolean             @default(true)
  created_at                    DateTime            @default(now())
  updated_at                    DateTime            @updatedAt

  permissionAccessPrivilege     PermissionAccessPrivilege[]

  @@map("mst_access_privilege")
}

model PermissionAccessPrivilege {
  id                            Int                 @id @default(autoincrement())
  permission_id                 Int
  mst_access_privilege_id       Int
  is_allow                      Boolean             @default(true)
  created_at                    DateTime            @default(now())
  updated_at                    DateTime            @updatedAt

  permission            Permission         @relation(fields: [permission_id], references: [id], onDelete: Cascade)
  mstAccessPrivilege    MstAccessPrivilege @relation(fields: [mst_access_privilege_id], references: [id])
  
  @@map("permission_access_privilege")
}

// model UserEmployee {
//   id                     Int                  @id @default(autoincrement())
//   prefix_name_id         Int
//   permission_id          Int
//   user_code              String               @unique @db.VarChar(20)
//   image_path             String?
//   username               String               @unique @db.VarChar(100)
//   password               String               @db.VarChar(100)
//   first_name             String?  
//   last_name              String?   
//   phone_number           String?              @db.VarChar(20)
//   email                  String               @unique @db.VarChar(255)
//   is_status              Boolean              @default(true)
//   is_active              Boolean              @default(true)
//   created_at             DateTime             @default(now())
//   updated_at             DateTime             @updatedAt
//   employeePrfile         EmployeePrfile?     // Use the correct relation name
//   prefixName             PrefixName          @relation(fields: [prefix_name_id], references: [id])
//   permission             Permission          @relation(fields: [permission_id], references: [id])
//   permissionUpdate       Permission[]        @relation("userEmpUpdate")
//   carInsurance           CarInsurance[]
//   customer               Customer[]
//   @@map("user_employee")               
// }



model Customer {
  id                                Int             @id @default(autoincrement())
    user_id                Int
  registration_type_id              Int
  prefix_name_id                    Int?
  license_plate_province_id         Int
  business_type_id                  Int
  phone_number                      String?        @db.VarChar(20)
  first_name                        String?
  last_name                         String?
  business_name                     String?
  address                           String?       @db.Text
  car_registration_number           String        @db.VarChar(50)
  is_active                         Boolean       @default(true)
  created_at                        DateTime      @default(now())
  updated_at                        DateTime      @updatedAt
  user             User             @relation(fields: [user_id], references: [id])
  registrationType          RegistrationType          @relation(fields: [registration_type_id], references: [id])
  prefixName                PrefixName?               @relation(fields: [prefix_name_id], references: [id])
  licensePlateProvince      LicensePlateProvince      @relation(fields: [license_plate_province_id], references: [id])
  businessType              BusinessType              @relation(fields: [business_type_id], references: [id])

  @@map("customer")
}



model CarCustomerImage{
  id                          Int           @id @default(autoincrement())
  car_insurance_customer_id   Int           
  image_path                  String

  carInsuranceCustomer      CarInsuranceCustomer     @relation(fields: [car_insurance_customer_id], references: [id],onDelete: Cascade)
  carCustomerImageType      EnumCarCustomerImageType

  @@map("car_customer_image")
}



enum EnumCarCustomerImageType{
      BACK
      FRONT
      LEFT
      RIGHT
      FRONTLEFT
      FRONTRIGHT
      REARLEFT
      REARRIGHT
      MILE
      ETC
}

enum EnumDriverType{
  DRIVER
  NO_DRIVER
}
