import { Transform, plainToClass, ClassConstructor } from 'class-transformer';

export function Parse<PERSON>son<T>(targetType: ClassConstructor<T>) {
  return Transform(({ value }) => {
    // Attempt to parse the value if it's a string
    
    if (typeof value === 'string') {
      try {
        const parsed = JSON.parse(value);
        // Use plainToClass to transform the parsed object into an instance of targetType
        return plainToClass(targetType, parsed);
      } catch (error) {
        console.error('JSON parsing error:', error);
        // Return the original string value if parsing fails
        return value;
      }
    }
    // If value is not a string, directly transform it assuming it's already in the correct shape
    return plainToClass(targetType, value);
  }, { toClassOnly: true });
}
