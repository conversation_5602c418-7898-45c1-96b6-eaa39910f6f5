import { PartialType } from '@nestjs/swagger';
import { CreateCarCodeDto } from './create-car-code.dto';
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateCarCodeDto extends PartialType(CreateCarCodeDto) {
  @ApiProperty({
    description: 'The name of the test.',
    example: 'รถยนต์ส่วนบุคคล-ส่วนบุคคล',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    example: '110',
    description: 'code of the  car code',
  })
  @IsString()
  code: string;

  @ApiProperty({
    description: 'Is status.',
    example: true,
  })
  @IsBoolean()
  is_status: boolean;

  @ApiProperty({
    description: 'Indicates whether the test is active.',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  is_active?: boolean;
}
