import { Controller, Get,  } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
} from "@nestjs/swagger";
@ApiTags("year")
@Controller('year')
export class YearController {
  constructor(
  ) { }

  @Get()
  @ApiOperation({ summary: "Get a year" })
 
  listYear() {
    const currentYear = new Date().getFullYear();
    const years = [];

    for (let year = currentYear; year >= 1900; year--) {
      years.push({ id: currentYear - year + 1, code: year, year: year });
    }

    return years; 
  }
}
