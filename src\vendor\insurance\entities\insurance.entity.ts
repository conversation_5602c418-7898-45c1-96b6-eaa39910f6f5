import { ApiProperty } from '@nestjs/swagger';

export class Insurance {
  @ApiProperty({
    example: 1,
    description: 'The unique identifier of the insurance',
  })
  id: number;

  @ApiProperty({
    example: 'T0001',
    description: 'The unique code of the insurance',
  })
  code: string;

  @ApiProperty({
    example: 'วิริยะ  ประกันภัย',
    description: 'The name of the insurance',
  })
  name: string;

  @ApiProperty({
    example: '/uploads/insurance/insurance.png',
    description: 'The image path for the insurance',
  })
  image_path: string;

  @ApiProperty({
    example: true,
    description: 'The status of the insurance (e.g., active or inactive)',
  })
  is_status: boolean;

  @ApiProperty({
    example: true,
    description: 'Indicates whether the insurance is active',
  })
  is_active: boolean;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the insurance was created',
  })
  created_at: Date;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the insurance was last updated',
  })
  updated_at: Date;
}
