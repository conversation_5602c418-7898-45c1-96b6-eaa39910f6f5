import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsDate, IsInt, IsNumber, IsOptional, IsString } from "class-validator";

export class UpdatePaymentDto{
    @IsInt()
    @IsOptional()
    @ApiProperty({ example: 1, description: 'The unique user_id of the payment', type: Number })
    user_id?: number;
  
    @IsInt()
    @IsOptional()
    @ApiProperty({ example: 1, description: 'The unique payment_method_id of the payment', type: Number })
    payment_method_id?: number;
  
    @IsInt()
    @IsOptional()
    @ApiProperty({ example: 1, description: 'The unique deejing_credit_id of the payment', type: Number })
    deejing_credit_id?: number;
  
    @IsString()
    @IsOptional()
    @ApiProperty({ example: 'PAY001', description: 'The unique payment_reference of the payment' })
    payment_reference?: string;
  
    @IsNumber({}, { each: true })
    @IsOptional()
    @ApiProperty({ example: 1000.00, description: 'The amount of the payment', type: Number })
    amount?: number;
  
    @IsString()
    @IsOptional()
    @ApiProperty({ example: 'paid', description: 'The payment_status of the payment' })
    payment_status?: string;
  
    @IsDate()
    @IsOptional()
    @ApiProperty({ example: '2024-08-22', description: 'The payment_date of the payment', type: Date })
    payment_date?: Date;
  
    @IsDate()
    @IsOptional()
    @ApiProperty({ example: '12:34:56', description: 'The payment_time of the payment', type: Date })
    payment_time?: Date;
  
    @IsString()
    @IsOptional()
    @ApiProperty({ type: 'string', format: 'binary', description: 'Image file for the payment proof' })
    payment_proof_path?: any;
  
    @IsBoolean()
    @IsOptional()
    @ApiProperty({ example: true, description: 'Indicates whether the payment is active', type: Boolean })
    is_active?: boolean;    
}