import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  UploadedFile,
  UseInterceptors,
  ParseIntPipe,
  Query,
} from "@nestjs/common";
import { UserRolePermissionService } from "./user-role-permission.service";
import { UserRolePermission } from "./entities/user-role-permission.entity";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";
import { FileInterceptor } from "@nestjs/platform-express";
import { diskStorage } from "multer";
import { CreateUserRolePermissionDto } from "./dto/create-user-role-permission.dto";
import { UpdateUserRolePermissionDto } from "./dto/update-user-role-permission.dto";
import { join } from "path";
import { FileService } from "@common/services/file.service"; // Assuming this is where your utility function is
import { ConvertService } from "@common/services/convert.service";
import { PaginationService } from "@common/services/pagination.service";
import { permission } from "process";

@ApiTags("user-role-permission")
@Controller("user-role-permission")
export class UserRolePermissionController {
  constructor(
    private readonly userRolePermissionService: UserRolePermissionService,
    private readonly paginationService: PaginationService,
    private fileService: FileService
  ) {}
  
/*
  @Post()
  @ApiOperation({ summary: "Create a new user role permission" })
  @ApiResponse({
    status: 201,
    description: "The user role permission has been successfully created.",
    type: CreateUserRolePermissionDto,
  })
   create(@Body() createUserRolePermissionDto: CreateUserRolePermissionDto) {
    return this.userRolePermissionService.create(createUserRolePermissionDto);
  } */

  @Put(":id")
  @ApiOperation({ summary: "Update a user role permission by ID" })
  @ApiResponse({
    status: 200,
    description: "The user role permission has been successfully updated.",
    type: UpdateUserRolePermissionDto,
  })
  /* async update(
    @Param("id", ParseIntPipe) id: number,
    @Body() updateUserRolePermissionDto: UpdateUserRolePermissionDto
  ) {
    return this.userRolePermissionService.update(id, updateUserRolePermissionDto);
  } */

  @Get()
  @ApiOperation({ summary: "Get a list of all user role permissions with pagination" })
  @ApiQuery({ name: "page", required: false, type: Number, example: 1 })
  @ApiQuery({ name: "perPage", required: false, type: Number, example: 10 })
  @ApiQuery({ name: "s", required: false, type: String, example: "camry" })
  @ApiResponse({
    status: 200,
    description: "The list of user role permissions has been successfully retrieved.",
    type: [UserRolePermission],
  })
  async pagination(
    @Query("page", ParseIntPipe) page: number = 1,
    @Query("perPage", ParseIntPipe) perPage: number = 10,
    @Query("s") s: string = ""
  ) {
    return this.paginationService.paginate(
      UserRolePermissionService.PATH_FILE,
      page,
      perPage,
      {
        name: {
          contains: s,
        },
      },
      { 
        permission: true 
      }
    );
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a single user role permission by ID" })
  @ApiResponse({
    status: 200,
    description: "The user role permission has been successfully retrieved.",
    type: UserRolePermission,
  })
  findOne(@Param("id", ParseIntPipe) id: number) {
    return this.userRolePermissionService.findOne(id);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a user role permission by ID" })
  @ApiResponse({
    status: 200,
    description: "The user role permission has been successfully deleted.",
    type: UserRolePermission,
  })
  remove(@Param("id", ParseIntPipe) id: number) {
    return this.userRolePermissionService.remove(id);
  }
}
