import { PrismaClient } from "@prisma/client";
const prisma = new PrismaClient();

const seedData = [
    { id: 1, code:"RT001", name: "รถจดทะเบียน" },
    { id: 2, code:"RT002", name: "ป้ายแดง" },
];

export async function registrationTypeSeeder() {
    await Promise.all(
        seedData.map(async (data) =>
        prisma.registrationType.upsert({
            where: { id: data.id },
            update: { name: data.name },
            create: data,
        })
        )
    );

    console.log('registrationTypeSeeder-Success')
}
