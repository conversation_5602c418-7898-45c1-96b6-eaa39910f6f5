import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  UploadedFile,
  UseInterceptors,
  ParseIntPipe,
  Query,
  UseGuards,
} from "@nestjs/common";
import { CarInsuranceService } from "./car-insurance.service";
import { CarInsurance } from "./entities/car-insurance.entity";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { CreateCarInsuranceDto } from "./dto/create-car-insurance.dto";
import { UpdateCarInsuranceDto } from "./dto/update-car-insurance.dto";
import { ConvertService } from "@common/services/convert.service";
import { CreateCarInsuranceCompareDto } from "src/car-insurance-compare/dto/create-car-insurance-compare.dto";
import { plainToInstance } from "class-transformer";
import { User } from "src/_decorators/user-decorator/user-decorator.decorator";
import { JwtAuthGuard } from "src/auth/jwt-auth.guard";

@ApiTags("car-insurance")
@Controller("car-insurance")
export class CarInsuranceController {
  constructor(
    private readonly carInsuranceService: CarInsuranceService,
  ) { }

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Create a new car insurance" })
  @ApiResponse({
    status: 201,
    description: "The car insurance has been successfully created.",
    type: CreateCarInsuranceDto,
  })
  create(@Body() createCarInsuranceDto: CreateCarInsuranceDto, @User() user: any) {
    return this.carInsuranceService.create(createCarInsuranceDto, user.id);
  }

  @Put(":id")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Update a car insurance by ID" })
  @ApiResponse({
    status: 200,
    description: "The car insurance has been successfully updated.",
    type: UpdateCarInsuranceDto,
  })
  async update(
    @Param("id", ParseIntPipe) id: number,
    @Body() updateCarInsuranceDto: UpdateCarInsuranceDto,
    @User() user: any
  ) {
    /* console.log('Car-Insurance-Update',updateCarInsuranceDto)
    console.log(
      'car_insurance_compares:',
      JSON.parse(JSON.stringify(updateCarInsuranceDto.car_insurance_compares))
    ); */
    return this.carInsuranceService.update(id, updateCarInsuranceDto, user.id);
  }


  @Get(":id")
  @ApiOperation({ summary: "Get a single car insurance by ID" })
  @ApiResponse({
    status: 200,
    description: "The car insurance has been successfully retrieved.",
    type: CarInsurance,
  })
  findOne(@Param("id", ParseIntPipe) id: number) {
    return this.carInsuranceService.findOne(id);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a car insurance by ID" })
  @ApiResponse({
    status: 200,
    description: "The car insurance has been successfully deleted.",
    type: CarInsurance,
  })
  remove(@Param("id", ParseIntPipe) id: number) {
    return this.carInsuranceService.remove(id);
  }
}
