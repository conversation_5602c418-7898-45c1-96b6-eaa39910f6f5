import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  UploadedFile,
  UseInterceptors,
  ParseIntPipe,
  Query,
  UseGuards,
  Req,
} from "@nestjs/common";
import { PermissionService } from "./permission.service";
import { Permission } from "./entities/permission.entity";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { FileInterceptor } from "@nestjs/platform-express";
import { diskStorage } from "multer";
import { CreatePermissionDto } from "./dto/create-permission.dto";
import { UpdatePermissionDto } from "./dto/update-permission.dto";
import { join } from "path";
import { FileService } from "@common/services/file.service"; // Assuming this is where your utility function is
import { ConvertService } from "@common/services/convert.service";
import { PaginationService } from "@common/services/pagination.service";
import { JwtAuthGuard } from "src/auth/jwt-auth.guard";
import { User } from "src/_decorators/user-decorator/user-decorator.decorator";

@ApiTags("permission")
@Controller("permission")
export class PermissionController {
  constructor(
    private readonly permissionService: PermissionService,
    private readonly paginationService: PaginationService,
  ) {}

  @Get("paginate")
  @ApiOperation({ summary: "Get a list of all permissions with pagination" })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'perPage', required: false, type: Number, example: 10 })
  @ApiQuery({ name: 'searchText', required: false, type: String, example: 'ADMIN' })
  @ApiResponse({
    status: 200,
    description: "The list of permissions has been successfully retrieved.",
    type: [Permission],
  })
  async pagination(
    @Query('page', ParseIntPipe) page: number = 1,
    @Query('perPage', ParseIntPipe) perPage: number = 10,
    @Query('searchText') searchText: string = ""
  ) {
    return this.paginationService.paginate(PermissionService.MODEL_NAME, page, perPage, {
      OR: [
        {name: { contains: searchText.toLowerCase() }},
        {lv_code: { contains: searchText.toLowerCase() }}
      ]
    });
  }

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Create a new permission" })
  @ApiResponse({
    status: 201,
    description: "The permission has been successfully created.",
    type: CreatePermissionDto,
  })
  create(@Body() createPermissionDto: CreatePermissionDto) {
    return this.permissionService.create(createPermissionDto);
  }

  @Get("/generate-code")
  @ApiOperation({ summary: "Get a permission generate code " })
  @ApiResponse({
    status: 200,
    description: "The permission code has been successfully retrieved.",
    type: Permission,
  })
  generateCodeNumber() {
    return this.permissionService.generateCodeNumber();
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get insurance type model All" })
  @ApiResponse({
    status: 200,
    description: "The insurance type has been successfully retrieved.",
    type: Permission,
  })
  findAll() {
    return this.permissionService.findAll();
  }

  @Get(":id")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get a single permission by ID" })
  @ApiResponse({
    status: 200,
    description: "The permission has been successfully retrieved.",
    type: Permission,
  })
  findOne(@Param("id", ParseIntPipe) id: number) {
    return this.permissionService.findOne(id);
  }

  @Put(":id")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Update a permission by ID" })
  @ApiResponse({
    status: 200,
    description: "The permission has been successfully updated.",
    type: UpdatePermissionDto,
  })
  update(
    // @Req() req,
    @Param("id", ParseIntPipe) id: number,
    @Body() updatePermissionDto: UpdatePermissionDto,
    @User() user : any,
  ) {
    // console.log('Req',req.user)
    return this.permissionService.update(user.id,id,updatePermissionDto);
  }

  @Delete(":id")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Delete a permission by ID" })
  @ApiResponse({
    status: 200,
    description: "The permission has been successfully deleted.",
    type: Permission,
  })
  remove(@Param("id", ParseIntPipe) id: number) {
    return this.permissionService.remove(id);
  }
}
