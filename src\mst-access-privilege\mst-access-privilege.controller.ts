import { Controller, Get, ParseIntPipe, Post, Query } from "@nestjs/common";
import { MstAccessPrivilegeService } from "./mst-access-privilege.service";
import { ApiConsumes, ApiOperation, ApiQuery, ApiResponse } from "@nestjs/swagger";
import { MstAccessPrivilege } from "./entities/mst-access-privilege.entity";

@Controller("mst-access-privilege")
export class MstAccessPrivilegeController {
  constructor(
    private readonly mstAccessPrivilageService: MstAccessPrivilegeService,
  ) {}

  @Get()
  @ApiOperation({ summary: "Get all mst access privilege " })
//   @ApiQuery({ name: "page", required: false, type: Number, example: 1 })
//   @ApiQuery({ name: "perPage", required: false, type: Number, example: 10 })
//   @ApiQuery({ name: "s", required: false, type: String, example: "camry" })
  @ApiResponse({
    status: 200,
    description: "The list of mst access privilege has been successfully retrieved.",
    type: [MstAccessPrivilege],
  })
  findAll() {
    return this.mstAccessPrivilageService.findAll();
  }
}
