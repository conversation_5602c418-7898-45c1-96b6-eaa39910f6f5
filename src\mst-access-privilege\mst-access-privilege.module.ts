import { Module } from '@nestjs/common';
import { MstAccessPrivilegeController } from './mst-access-privilege.controller';
import { MstAccessPrivilegeService } from './mst-access-privilege.service';
import { PrismaModule } from 'src/prisma/prisma.module';

@Module({
  controllers: [MstAccessPrivilegeController],
  providers: [MstAccessPrivilegeService],
  imports: [PrismaModule]
})
export class MstAccessPrivilegeModule {}
