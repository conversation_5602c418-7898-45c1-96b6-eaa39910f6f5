import { ConvertService } from "@common/services/convert.service";
import { FileService } from "@common/services/file.service"; // Assuming this is where your utility function is
import { PaginationService } from "@common/services/pagination.service";
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UploadedFiles,
  UseInterceptors,
} from "@nestjs/common";
import {
  FileFieldsInterceptor,
  FilesInterceptor,
} from "@nestjs/platform-express";
import {
  ApiConsumes,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import path from "path";
import { HWC_UPLOAD_PATH } from "src/configs/server";
import { HwcFileUploadService } from "src/hwc-file-upload/hwc-file-upload.service";
import { v4 as uuidv4 } from "uuid";
import { CreateInsuranceDto } from "./dto/create-insurance.dto";
import { UpdateInsuranceDto } from "./dto/update-insurance.dto";
import { Insurance } from "./entities/insurance.entity";
import { InsuranceService } from "./insurance.service";

@ApiTags("insurance")
@Controller("insurance")
export class InsuranceController {
  constructor(
    private readonly insuranceService: InsuranceService,
    private readonly paginationService: PaginationService,
    private fileService: FileService,
    private readonly hwcFileUploadService: HwcFileUploadService
  ) {}

  @Post()
  @ApiOperation({ summary: "Create a new insurance" })
  @ApiConsumes("multipart/form-data")
  @ApiResponse({
    status: 201,
    description: "The insurance has been successfully created.",
    type: CreateInsuranceDto,
  })
  @UseInterceptors(
    FileFieldsInterceptor([{ name: "image_file", maxCount: 1 }]),
    FilesInterceptor
  )
  async create(
    @UploadedFiles()
    files: {
      image_file?: Express.Multer.File[];
    },
    @Body() createInsuranceDto: CreateInsuranceDto
  ) {
    const imageFile = files?.image_file?.[0];

    if (imageFile) {
      const extension = path.extname(imageFile.originalname);
      const keyGenerate = `${HWC_UPLOAD_PATH}/${uuidv4()}${extension}`;
      try {
        await this.hwcFileUploadService.uploadFile({
          key: keyGenerate,
          file: imageFile,
          success_action_status: "200",
        });
      } catch (error) {
        console.error("File upload failed:", error.message);
        throw error;
      }
      createInsuranceDto.image_path = keyGenerate;
    }

    return this.insuranceService.create(createInsuranceDto);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update a insurance by ID" })
  @ApiConsumes("multipart/form-data")
  @ApiResponse({
    status: 200,
    description: "The insurance has been successfully updated.",
    type: UpdateInsuranceDto,
  })
  @UseInterceptors(
    FileFieldsInterceptor([{ name: "image_file", maxCount: 1 }]),
    FilesInterceptor
  )
  async update(
    @Param("id", ParseIntPipe) id: number,
    @UploadedFiles()
    files: {
      image_file?: Express.Multer.File[];
    },
    @Body() updateInsuranceDto: UpdateInsuranceDto
  ) {
    // Convert string fields to boolean
    updateInsuranceDto.is_status = ConvertService.stringToBoolean(
      updateInsuranceDto.is_status
    );
    updateInsuranceDto.is_active = ConvertService.stringToBoolean(
      updateInsuranceDto.is_active
    );

    const imageFile = files?.image_file?.[0];

    const existingInsurance = await this.insuranceService.findOne(id);

    if (imageFile) {
      const extension = path.extname(imageFile.originalname);
      const keyGenerate = `${HWC_UPLOAD_PATH}/${uuidv4()}${extension}`;
      try {
        await this.hwcFileUploadService.uploadFile({
          key: keyGenerate,
          file: imageFile,
          success_action_status: "200",
        });
      } catch (error) {
        console.error("File upload failed:", error.message);
        throw error;
      }
      updateInsuranceDto.image_path = keyGenerate;
    }

    return this.insuranceService.update(id, updateInsuranceDto);
  }

  @Get("/generate-code")
  @ApiOperation({ summary: "Get a insurance generate code" })
  @ApiResponse({
    status: 200,
    description: "The insurance code has been successfully retrieved.",
    // type: ,
  })
  generateCodeNumber() {
    return this.insuranceService.generateCodeNumber();
  }

  @Get("paginate")
  @ApiOperation({ summary: "Get a list of all insurance with pagination" })
  @ApiQuery({ name: "page", required: false, type: Number, example: 1 })
  @ApiQuery({ name: "perPage", required: false, type: Number, example: 10 })
  @ApiQuery({
    name: "searchText",
    required: false,
    type: String,
    example: "วิริยะ ",
  })
  @ApiResponse({
    status: 200,
    description: "The list of insurance has been successfully retrieved.",
    type: [Insurance],
  })
  async paginationAdmin(
    @Query("page", ParseIntPipe) page: number = 1,
    @Query("perPage", ParseIntPipe) perPage: number = 10,
    @Query("searchText") searchText: string = ""
  ) {
    return this.paginationService.paginate(
      InsuranceService.MODEL_NAME,
      page,
      perPage,
      {
        OR: [
          { name: { contains: searchText.toLowerCase() } },
          { code: { contains: searchText.toLowerCase() } },
        ],
      }
    );
  }

  @Get("mock-data")
  async mockData(
    @Query("repairType") repairType?: string,
    @Query("value") value?: string,
    @Query("firstPartDmg") firstPartDmg?: string,
    @Query("minInsuranceValue") minInsuranceValue?: string,
    @Query("maxInsuranceValue") maxInsuranceValue?: string,
    @Query("insuranceAddonService") insuranceAddonService?: string,
    @Query("company") company?: string,
    @Query("sortField") sortField?: string
  ) {
    const values = value ? parseFloat(value.replace(/,/g, "")) : undefined;
    const firstPartDmgs = firstPartDmg
      ? firstPartDmg.split(",").map((id) => parseInt(id.trim(), 10))
      : undefined;
    const min =
      minInsuranceValue !== undefined
        ? parseFloat(minInsuranceValue)
        : undefined;
    const max =
      maxInsuranceValue !== undefined
        ? parseFloat(maxInsuranceValue)
        : undefined;
    const addonServiceIds = insuranceAddonService
      ? insuranceAddonService.split(",").map((id) => parseInt(id.trim(), 10))
      : undefined;
    const companys = company
      ? company.split(",").map((id) => parseInt(id.trim(), 10))
      : undefined;

    /* console.log('firstPartDmg',firstPartDmgs)
    console.log('min',min)
    console.log('max',max)
    console.log('addons',addonServiceIds);
    console.log('company',companys) */

    return this.insuranceService.getMockData(
      repairType,
      values,
      firstPartDmgs,
      min,
      max,
      addonServiceIds,
      companys,
      sortField
    );
  }

  /* @Get()
  @ApiOperation({ summary: "Get insurance type model All" })
  @ApiResponse({
    status: 200,
    description: "The insurance type has been successfully retrieved.",
    type: Insurance,
  })
  findAll() {
    console.log('Try to get insurance');
    return this.insuranceService.findAll();
  } */
  @Get()
  @ApiOperation({ summary: "Get insurance type model All" })
  @ApiResponse({
    status: 200,
    description: "The insurance type has been successfully retrieved.",
    type: Insurance,
  })
  async findAll(@Query("filterText") filterText?: string) {
    // console.log('Try to get insurance: ',filterText);
    return this.insuranceService.findAll(filterText);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a single car brand by ID" })
  @ApiResponse({
    status: 200,
    description: "The car brand has been successfully retrieved.",
    type: Insurance,
  })
  findOne(@Param("id", ParseIntPipe) id: number) {
    return this.insuranceService.findOne(id);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a car brand by ID" })
  @ApiResponse({
    status: 200,
    description: "The car brand has been successfully deleted.",
    type: Insurance,
  })
  remove(@Param("id", ParseIntPipe) id: number) {
    return this.insuranceService.remove(id);
  }
}
