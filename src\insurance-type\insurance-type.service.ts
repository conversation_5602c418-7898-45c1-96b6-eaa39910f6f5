import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';

import { InsuranceType } from '@prisma/client';
import { Prisma } from '@prisma/client';
import { join } from 'path';
import { CreateInsuranceTypeDto } from './dto/create-insurance-type.dto';
import { UpdateInsuranceTypeDto } from './dto/update-insurance-type.dto';
import { HandleErrorService } from "@common/services/handle-error.services";

@Injectable()
export class InsuranceTypeService {
  public static MODEL_NAME = 'insuranceType';
  constructor(private prisma: PrismaService,
    private readonly handleErrorService: HandleErrorService) { }


  async create(data: CreateInsuranceTypeDto): Promise<InsuranceType> {
    try {
      return await this.prisma.insuranceType.create({
        data
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }

  }

  async findAll(): Promise<InsuranceType[]> {
    return await this.prisma.insuranceType.findMany({
      where: {
        is_status: true,
        is_active: true
      },
      include: {
        place: true
      }
    });
  }

  async findOne(id: number): Promise<InsuranceType> {
    return this.prisma.insuranceType.findUnique({ where: { id, is_active: true }, include: { place: true }, });
  }

  async update(id: number, data: UpdateInsuranceTypeDto): Promise<InsuranceType> {
    try {
      return await this.prisma.insuranceType.update({
        where: { id},
        data
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }

  }

  async remove(id: number): Promise<InsuranceType> {
    const existData = await this.prisma.carCode.findUnique({ where: { id, is_active: true } });
    const date = Date.now();
    return this.prisma.insuranceType.update({
      where: { id },
      data: {
        code: date+'-delete-'+existData.code,
        is_active: false,
      },
    });
  }
}
