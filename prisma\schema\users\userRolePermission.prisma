model UserRolePermission {
    id            Int        @id @default(autoincrement())
    permission_id Int
    user_role_id  Int
    is_granted    Boolean
    is_active     Boolean    @default(true)
    is_status     Boolean    @default(true)
    created_at    DateTime   @default(now())
    updated_at    DateTime   @updatedAt
    permission    Permission @relation(fields: [permission_id], references: [id])
    user_role     UserRole   @relation(fields: [user_role_id], references: [id])

    @@unique([user_role_id, permission_id])
    @@map("user_role_permission")
}
