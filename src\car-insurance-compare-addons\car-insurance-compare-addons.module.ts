import { Module } from '@nestjs/common';
import { CarInsuranceCompareAddonsController } from './car-insurance-compare-addons.controller';
import { CarInsuranceCompareAddonsService } from './car-insurance-compare-addons.service';
import { PrismaModule } from 'src/prisma/prisma.module';

@Module({
  controllers: [CarInsuranceCompareAddonsController],
  providers: [CarInsuranceCompareAddonsService],
  imports: [PrismaModule]
})
export class CarInsuranceCompareAddonsModule {}
