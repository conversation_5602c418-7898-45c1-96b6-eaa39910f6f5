/*
  Warnings:

  - You are about to alter the column `quotation_no` on the `car_insurance` table. The data in that column could be lost. The data in that column will be cast from `VarChar(191)` to `VarChar(20)`.
  - A unique constraint covering the columns `[quotation_no]` on the table `car_insurance` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE `car_insurance` MODIFY `quotation_no` VARCHAR(20) NULL;

-- CreateIndex
CREATE UNIQUE INDEX `car_insurance_quotation_no_key` ON `car_insurance`(`quotation_no`);
