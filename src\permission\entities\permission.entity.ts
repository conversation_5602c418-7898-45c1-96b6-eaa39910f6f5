import { ApiProperty } from '@nestjs/swagger';

export class Permission {
  @ApiProperty({
    example: 1,
    description: 'The unique identifier of the permission',
  })
  id: number;

  @ApiProperty({
    example: null,
    description: 'The parent ID of the permission, if any',
    type: Number,
  })
  parent_id?: number;

  @ApiProperty({
    example: 'P0001',
    description: 'The unique code of the permission',
  })
  code: string;

  @ApiProperty({
    example: 'Manage Users',
    description: 'The name of the permission',
  })
  name: string;

  @ApiProperty({
    example: true,
    description: 'Indicates whether the permission is active',
  })
  is_active: boolean;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the permission was created',
  })
  created_at: Date;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the permission was last updated',
  })
  updated_at: Date;
}