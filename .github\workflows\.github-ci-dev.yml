name: AMN CI

on:
  push:
    branches: [ dev ]

jobs:
  build:
    runs-on: self-hosted
    steps:
      - name: Set Vars
        id: setvars
        run: |
            echo "PROJECT_REPO=deejing-dev-api" >> $GITHUB_OUTPUT
            echo "PROJECT_NAME=DeeJing - API" >> $GITHUB_OUTPUT
            echo "PROJECT_DOMAIN_URL=https://deejing-dev-api.seamlesssolution.co/" >> $GITHUB_OUTPUT
            echo "APP_ENV=DEV" >> $GITHUB_OUTPUT
            echo "WEBHOOK_DOMAIN=dev-panel.seamlesssolution.co" >> $GITHUB_OUTPUT
            echo "WEBHOOK_KEY=1525c2a101bf55f7151d63a9299a35c30cb39d275411f251" >> $GITHUB_OUTPUT
            echo "CLOUDFLARE_ZONE_ID=1df3ace8bd313660034df6c92ec3d0c8" >> $GITHUB_OUTPUT
      
      - name: Checkout
        uses: actions/checkout@v2
        with: 
          fetch-depth: 0 

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Get current time
        uses: josStorer/get-current-time@v2
        id: currenttime
        with:
          format: YYYYMMDD-HHmmSS
          utcOffset: "+07:00"

      - name: Build and push
        id: build
        uses: docker/build-push-action@v4
        continue-on-error: true
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: |
            ${{ secrets.DOCKER_HUB_USERNAME }}/${{ steps.setvars.outputs.PROJECT_REPO }}:${{ steps.currenttime.outputs.formattedTime }}
            ${{ secrets.DOCKER_HUB_USERNAME }}/${{ steps.setvars.outputs.PROJECT_REPO }}:latest

      # - name: Call Cloudflare API to Purge all cache
      #   run: |
      #     curl --location --request POST 'https://api.cloudflare.com/client/v4/zones/${{ steps.currenttime.outputs.CLOUDFLARE_ZONE_ID }}/purge_cache' \
      #     --header 'X-Auth-Email: ${{ secrets.CLOUDFLARE_X_AUTH_EMAIL }}' \
      #     --header 'X-Auth-Key: ${{ secrets.CLOUDFLARE_X_AUTH_KEY }}' \
      #     --header 'Content-Type: application/json' \
      #     --header 'Authorization: Bearer ${{ secrets.CLOUDFLARE_AUTH_BEARER }}' \
      #     --header 'Cookie: __cflb=0H28vgHxwvgAQtjUGU56Rb8iNWZVUvXhhjo1cUEPJv1; __cfruid=a74c632a9e3467afd7cad030ee92e03cb5d8da27-1664779597' \
      #     --data-raw '{"purge_everything":true}'

      - name: Call Webhook to Update Service
        id: callwebhook
        if: steps.build.outcome == 'success'
        continue-on-error: true
        run: |

          CODE=`curl --write-out '%{http_code}' \
              --silent \
              --output /dev/null \
              --request GET \
              --url 'https://${{ steps.setvars.outputs.WEBHOOK_DOMAIN }}/api/deploy/${{ steps.setvars.outputs.WEBHOOK_KEY }}'`


          if [ $CODE != "200" ] 
          then
              echo $CODE
              echo "FAILURE"
              exit 1
          else
              echo $CODE
              echo "SUCCESS"
              exit 0
          fi

      - name: Sent LINE Noti - Success
        if: steps.build.outcome == 'success' && steps.callwebhook.outcome == 'success'
        uses: snow-actions/line-notify@v1.1.0
        with:
          access_token: ${{ secrets.LINE_ACCESS_TOKEN }}
          message: |
            👏 ${{ steps.setvars.outputs.PROJECT_NAME }} (${{ steps.setvars.outputs.APP_ENV }}) | Build and Deploy
            มีการอัปเดตเรื่อง ${{ github.event.commits[0].message }} ของ ${{ github.event.commits[0].author.name }} เสร็จแล้ว
            รอโปรแกรม Compile ประมาณ 2 นาที แล้วทดสอบได้เลย 😎
            ${{ steps.setvars.outputs.PROJECT_DOMAIN_URL }}

      - name: Sent LINE Noti - Failure
        if: steps.build.outcome != 'success' || steps.callwebhook.outcome != 'success'
        uses: snow-actions/line-notify@v1.1.0
        with:
          access_token: ${{ secrets.LINE_ACCESS_TOKEN }}
          message: |
            ❌❌❌ ${{ steps.setvars.outputs.PROJECT_NAME }} (${{ steps.setvars.outputs.APP_ENV }}) - Build Error
            เรื่อง ${{ github.event.commits[0].message }} ของ ${{ github.event.commits[0].author.name }} โปรดตรวจสอบ

