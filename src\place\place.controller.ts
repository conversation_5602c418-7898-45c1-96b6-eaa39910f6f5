import {
  Controller,
  Get,
} from "@nestjs/common";
import { PlaceService } from "./place.service";
import { Place } from "./entities/place.entity";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
} from "@nestjs/swagger";



@ApiTags("place")
@Controller("place")
export class PlaceController {
  constructor(
    private readonly placeService: PlaceService,
  ) { }

  @Get("")
  @ApiOperation({ summary: "Get All Place" })
  @ApiResponse({
    status: 200,
    description: "The place has been successfully retrieved.",
    type: Place,
  })
  findAll() {
    return this.placeService.findAll();
  }
}
