import { PrismaClient } from "@prisma/client";
const prisma = new PrismaClient();

const seedData = [
    { id: 1, name: "Dashboards" },
    { id: 2, name: "ระบบเช็คเบี้ย" },
    { id: 3, name: "จัดการใบเสนอราคา"},
    { id: 4, name: "จัดการสมาชิก"},
    { id: 5, name: "จัดการ Subagent"},
    { id: 6, name: "การเงิน"},
    { id: 7, name: "จัดการบริษัทประกัน"},
    { id: 8, name: "จัดการรถยนต์"},
    { id: 9, name: "จัดการบทความ"},
    { id: 10, name: "จัดการคำถามที่พบบ่อย"},
    { id: 11, name: "จัดการหน้าประกันภัย"},
    { id: 12, name: "จัดการรีวิวลูกค้า"},
    { id: 13, name: "จัดการหน้าติดต่อเรา"},
    { id: 14, name: "จัดการผู้ใช้งาน"},
    { id: 15, name: "จัดการระดับผู้ใช้งาน"},
    { id: 16, name: "การตั้งค่า PIN"},
];

export async function mstAccessPrivilegeSeeder() {
    await Promise.all(
        seedData.map(async (data) =>
        prisma.mstAccessPrivilege.upsert({
            where: { id: data.id },
            update: { name: data.name },
            create: data,
        })
        )
    );

    console.log('mstAccessPrivilegeSeeder-Success')
}
