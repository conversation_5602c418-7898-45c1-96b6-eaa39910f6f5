import { ApiProperty } from '@nestjs/swagger';

export class Admin {
  @ApiProperty({ example: 1, required: true })
  user_id: number;

  @ApiProperty({ type: 'string', required: false })
  image_path?: string;

  @ApiProperty({ example: 1, required: false })
  prefix_name_id?: number;

  @ApiProperty({ example: "เจตรินทร์", required: true })
  first_name: string;

  @ApiProperty({ example: "สีแตง", required: false })
  last_name: string;

  @ApiProperty({ example: true, required: false, type: Boolean })
  is_status?: boolean = true;

  @ApiProperty({ example: true, required: false, type: <PERSON>ole<PERSON> })
  is_active?: boolean = true;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the car brand was created',
  })
  created_at: Date;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the car brand was last updated',
  })
  updated_at: Date;
}
