/*
  Warnings:

  - You are about to drop the column `user_id` on the `car_insurance` table. All the data in the column will be lost.
  - Added the required column `user_employee_id` to the `car_insurance` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `car_insurance` DROP FOREIGN KEY `car_insurance_user_id_fkey`;

-- AlterTable
ALTER TABLE `car_insurance` DROP COLUMN `user_id`,
    ADD COLUMN `user_employee_id` INTEGER NOT NULL;

-- AddForeignKey
ALTER TABLE `car_insurance` ADD CONSTRAINT `car_insurance_user_employee_id_fkey` FOREIGN KEY (`user_employee_id`) REFERENCES `user_employee`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
