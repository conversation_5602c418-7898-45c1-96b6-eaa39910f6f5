import { ApiProperty } from '@nestjs/swagger';

import {UserRolePermission} from '../../user-role-permission/entities/user-role-permission.entity'

export class UserRole {
  @ApiProperty({
    example: 1,
    description: 'The unique identifier of the user role',
  })
  id: number;

  @ApiProperty({
    example: 'P0001',
    description: 'The unique code of the user role',
  })
  code: string;

  @ApiProperty({
    example: 'Manage Users',
    description: 'The name of the user role',
  })
  name: string;

  @ApiProperty({
    example: true,
    description: 'Indicates whether the user role is active',
  })
  is_active: boolean;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the user role was created',
  })
  created_at: Date;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the user role was last updated',
  })
  updated_at: Date;

  @ApiProperty({
    type: () => [UserRolePermission],
    description: 'List of permissions associated with the user role',
    isArray: true,
  })
  user_role_permission: UserRolePermission[];
}