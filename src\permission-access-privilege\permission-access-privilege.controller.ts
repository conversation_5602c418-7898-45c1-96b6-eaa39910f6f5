import { Controller, Get } from '@nestjs/common';
import { PermissionAccessPrivilegeService } from './permission-access-privilege.service';
import { ApiOperation, ApiQuery, ApiResponse } from '@nestjs/swagger';
import { PermissionAccessPrivilege } from './entities/permission-access-privilege.entity';

@Controller('permission-access-privilege')
export class PermissionAccessPrivilegeController {
    constructor(
        private readonly PermissionAccessPrivilegeService: PermissionAccessPrivilegeService,
    ) { }

    @ApiOperation({ summary: "Get all mst access privilege " })
    @ApiResponse({
    status: 200,
    description: "The list of mst access privilege has been successfully retrieved.",
    type: [PermissionAccessPrivilege],
    })
    findAll() {
        return this.PermissionAccessPrivilegeService.findAll();
    }
}
