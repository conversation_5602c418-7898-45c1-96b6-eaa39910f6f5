/*
  Warnings:

  - You are about to alter the column `year` on the `car_model` table. The data in that column could be lost. The data in that column will be cast from `Var<PERSON>har(255)` to `Int`.
  - You are about to drop the column `image_path` on the `user` table. All the data in the column will be lost.
  - You are about to drop the `car_sub_model` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `test_table` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[user_id]` on the table `admin` will be added. If there are existing duplicate values, this will fail.
  - Made the column `image_path` on table `admin` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE `car_sub_model` DROP FOREIGN KEY `car_sub_model_car_brand_id_fkey`;

-- DropForeignKey
ALTER TABLE `car_sub_model` DROP FOREIGN KEY `car_sub_model_car_model_id_fkey`;

-- DropIndex
DROP INDEX `car_code_code_key` ON `car_code`;

-- AlterTable
ALTER TABLE `admin` MODIFY `image_path` VARCHAR(255) NOT NULL;

-- AlterTable
ALTER TABLE `car_model` MODIFY `year` INTEGER NOT NULL;

-- AlterTable
ALTER TABLE `user` DROP COLUMN `image_path`;

-- DropTable
DROP TABLE `car_sub_model`;

-- DropTable
DROP TABLE `test_table`;

-- CreateTable
CREATE TABLE `car_insurance` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NOT NULL,
    `car_brand_id` INTEGER NOT NULL,
    `car_model_id` INTEGER NOT NULL,
    `car_submodel_id` INTEGER NULL,
    `insurance_type_id` INTEGER NOT NULL,
    `license_plate_province_id` INTEGER NOT NULL,
    `car_code_id` INTEGER NOT NULL,
    `car_insurance_status_id` INTEGER NOT NULL,
    `year` INTEGER NOT NULL,
    `original_insurance_value` DECIMAL(15, 2) NULL,
    `current_insurance_value` DECIMAL(15, 2) NOT NULL,
    `reminder_status` VARCHAR(50) NULL,
    `job_status` VARCHAR(50) NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `car_insurance_status` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `color` VARCHAR(20) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `insurance_addon_service` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `code` VARCHAR(50) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `insurance_addon_service_code_key`(`code`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `car_submodel` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `car_brand_id` INTEGER NOT NULL,
    `car_model_id` INTEGER NOT NULL,
    `code` VARCHAR(50) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `year` INTEGER NOT NULL,
    `is_status` BOOLEAN NOT NULL DEFAULT true,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `car_submodel_code_key`(`code`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `insurance` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `code` VARCHAR(50) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `image_path` VARCHAR(255) NOT NULL,
    `is_status` BOOLEAN NOT NULL DEFAULT true,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `insurance_code_key`(`code`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `agent` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NOT NULL,
    `code` VARCHAR(50) NOT NULL,
    `image_path` VARCHAR(191) NULL,
    `fullname` VARCHAR(191) NOT NULL,
    `phone_number` VARCHAR(20) NOT NULL,
    `password` VARCHAR(191) NOT NULL,
    `email` VARCHAR(191) NOT NULL,
    `contact_email` VARCHAR(191) NULL,
    `is_insurance_broker_license` BOOLEAN NOT NULL,
    `referral_code` VARCHAR(50) NULL,
    `agent_type_id` INTEGER NOT NULL,
    `occupation_id` INTEGER NOT NULL,
    `insurance_sales_experience_id` INTEGER NOT NULL,
    `birthdate` DATE NULL,
    `gender` VARCHAR(10) NULL,
    `id_card_house_number` VARCHAR(50) NULL,
    `id_card_address` VARCHAR(191) NULL,
    `id_card_sub_district_id` INTEGER NOT NULL,
    `id_card_zip_code` VARCHAR(10) NULL,
    `document_delivery_status` VARCHAR(50) NULL,
    `delivery_house_number` VARCHAR(50) NULL,
    `delivery_address` VARCHAR(191) NULL,
    `delivery_sub_district_id` INTEGER NOT NULL,
    `delivery_zip_code` VARCHAR(10) NULL,
    `card_number` VARCHAR(20) NOT NULL,
    `id_card_image_path` VARCHAR(191) NULL,
    `broker_license_number` VARCHAR(50) NOT NULL,
    `broker_license_expiry_date` DATE NULL,
    `broker_license_image_path` VARCHAR(191) NULL,
    `bank_name` VARCHAR(100) NOT NULL,
    `branch_code` VARCHAR(50) NOT NULL,
    `bank_account_number` VARCHAR(100) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `agent_code_key`(`code`),
    UNIQUE INDEX `agent_email_key`(`email`),
    UNIQUE INDEX `agent_card_number_key`(`card_number`),
    UNIQUE INDEX `agent_bank_account_number_key`(`bank_account_number`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `agent_type` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `is_status` BOOLEAN NOT NULL DEFAULT true,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `bank_account` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `payment_id` INTEGER NOT NULL,
    `bank_logo` VARCHAR(191) NULL,
    `bank_name` VARCHAR(100) NULL,
    `branch_name` VARCHAR(100) NULL,
    `account_number` VARCHAR(50) NOT NULL,
    `account_name` VARCHAR(100) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `business_type` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name_th` VARCHAR(191) NULL,
    `name_en` VARCHAR(191) NULL,
    `prefix_name_th` VARCHAR(191) NULL,
    `prefix_name_en` VARCHAR(191) NULL,
    `after_name_th` VARCHAR(191) NULL,
    `after_name_en` VARCHAR(191) NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `car_act` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NOT NULL,
    `insurance_id` INTEGER NOT NULL,
    `car_code_id` INTEGER NOT NULL,
    `payment_id` INTEGER NULL,
    `car_registration_number` VARCHAR(50) NOT NULL,
    `expiration_date` DATE NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `car_act_payment` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `car_act_id` INTEGER NOT NULL,
    `payment_id` INTEGER NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `car_act_policy_detail` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `car_act_id` INTEGER NOT NULL,
    `license_plate_province_id` INTEGER NOT NULL,
    `car_brand_id` INTEGER NOT NULL,
    `car_model_id` INTEGER NOT NULL,
    `coverage_start_date` DATE NOT NULL,
    `coverage_end_date` DATE NOT NULL,
    `chassis_number` VARCHAR(100) NOT NULL,
    `year` INTEGER NOT NULL,
    `cc` INTEGER NOT NULL,
    `weight` DECIMAL(10, 2) NULL,
    `color_id` INTEGER NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `car_act_policy_page_detail` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `car_act_id` INTEGER NOT NULL,
    `province_id` INTEGER NOT NULL,
    `prefix_id` INTEGER NOT NULL,
    `id_card_type` VARCHAR(50) NOT NULL,
    `id_card_number` VARCHAR(20) NOT NULL,
    `first_name` VARCHAR(100) NOT NULL,
    `last_name` VARCHAR(100) NOT NULL,
    `gender` VARCHAR(10) NOT NULL,
    `phone_number` VARCHAR(20) NOT NULL,
    `nationality` VARCHAR(50) NOT NULL,
    `house_number` VARCHAR(50) NULL,
    `address` VARCHAR(191) NULL,
    `sub_district` VARCHAR(100) NOT NULL,
    `district` VARCHAR(100) NOT NULL,
    `zip_code` VARCHAR(10) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `car_insurance_addons` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `car_insurance_id` INTEGER NOT NULL,
    `insurance_addon_service_id` INTEGER NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `car_insurance_compare` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `car_insurance_id` INTEGER NOT NULL,
    `insurance_id` INTEGER NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `car_insurance_compare_addons` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `car_insurance_compare_id` INTEGER NOT NULL,
    `insurance_addon_service_id` INTEGER NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `car_insurance_compare_detail` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `insurance_id` INTEGER NOT NULL,
    `insurance_type_id` INTEGER NOT NULL,
    `car_insurance_compare_id` INTEGER NOT NULL,
    `is_named_driver` BOOLEAN NOT NULL DEFAULT true,
    `is_excess` BOOLEAN NOT NULL DEFAULT true,
    `price` DECIMAL(15, 2) NULL,
    `vehicle_damage` DECIMAL(15, 2) NULL,
    `vehicle_loss_or_fire` DECIMAL(15, 2) NULL,
    `external_person_life_per_person` DECIMAL(15, 2) NULL,
    `external_person_life_per_case` DECIMAL(15, 2) NULL,
    `external_person_property` DECIMAL(15, 2) NULL,
    `excess_property_damage` DECIMAL(15, 2) NULL,
    `personal_accident` DECIMAL(15, 2) NULL,
    `medical_expense_per_person` DECIMAL(15, 2) NULL,
    `driver_bail` DECIMAL(15, 2) NULL,
    `current_insurance_value` DECIMAL(15, 2) NULL,
    `marketing_cost` DECIMAL(15, 2) NULL,
    `excess` DECIMAL(15, 2) NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `car_insurance_customer` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `car_insurance_id` INTEGER NOT NULL,
    `registration_type_id` INTEGER NOT NULL,
    `prefix_name_id` INTEGER NULL,
    `province_id` INTEGER NOT NULL,
    `business_type_id` INTEGER NOT NULL,
    `phone_number` VARCHAR(20) NOT NULL,
    `first_name` VARCHAR(191) NOT NULL,
    `last_name` VARCHAR(191) NULL,
    `address` TEXT NULL,
    `car_registration_number` VARCHAR(50) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `car_insurance_detail` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `insurance_id` INTEGER NOT NULL,
    `insurance_type_id` INTEGER NOT NULL,
    `car_insurance_id` INTEGER NOT NULL,
    `is_named_driver` BOOLEAN NOT NULL DEFAULT false,
    `is_excess` BOOLEAN NOT NULL DEFAULT false,
    `price` DECIMAL(15, 2) NULL,
    `vehicle_damage` DECIMAL(15, 2) NULL,
    `vehicle_loss_or_fire` DECIMAL(15, 2) NULL,
    `external_person_life_per_person` DECIMAL(15, 2) NULL,
    `external_person_life_per_case` DECIMAL(15, 2) NULL,
    `external_person_property` DECIMAL(15, 2) NULL,
    `excess_property_damage` DECIMAL(15, 2) NULL,
    `personal_accident` DECIMAL(15, 2) NULL,
    `medical_expense_per_person` DECIMAL(15, 2) NULL,
    `driver_bail` DECIMAL(15, 2) NULL,
    `current_insurance_value` DECIMAL(15, 2) NULL,
    `marketing_cost` DECIMAL(15, 2) NULL,
    `excess` DECIMAL(15, 2) NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `car_insurance_payment` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `car_insurance_id` INTEGER NOT NULL,
    `payment_id` INTEGER NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `deejing_credit` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NOT NULL,
    `balance` DECIMAL(15, 2) NOT NULL DEFAULT 0.00,
    `status` VARCHAR(50) NOT NULL DEFAULT 'active',
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `deejing_credit_transaction_log` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `deejing_credit_id` INTEGER NOT NULL,
    `transaction_type` VARCHAR(50) NOT NULL,
    `amount` DECIMAL(15, 2) NOT NULL,
    `transaction_reference` VARCHAR(191) NULL,
    `previous_balance` DECIMAL(15, 2) NOT NULL,
    `new_balance` DECIMAL(15, 2) NOT NULL,
    `description` TEXT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `district` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `code` VARCHAR(50) NOT NULL,
    `name_th` VARCHAR(191) NOT NULL,
    `name_en` VARCHAR(191) NULL,
    `province_id` INTEGER NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `district_code_key`(`code`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `insurance_sales_experience` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `marketing_fee_rate_act` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NOT NULL,
    `car_code_id` INTEGER NOT NULL,
    `insurance_id` INTEGER NOT NULL,
    `marketing_fee_rate_act_type_id` INTEGER NOT NULL,
    `percent` DECIMAL(5, 2) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `marketing_fee_rate_act_type` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `marketing_fee_rate_insurance` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NOT NULL,
    `insurance_type_id` INTEGER NOT NULL,
    `insurance_id` INTEGER NOT NULL,
    `percent` DECIMAL(5, 2) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `mws_cmi_vehicle_type` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `cmi_veh_type_code` VARCHAR(50) NOT NULL,
    `oic_description_th` VARCHAR(191) NOT NULL,
    `tsri_description_th` VARCHAR(191) NOT NULL,
    `tsri_description_en` VARCHAR(191) NOT NULL,
    `scale_type` VARCHAR(2) NOT NULL,
    `capacity_min` INTEGER NOT NULL,
    `capacity_max` INTEGER NOT NULL,
    `code_type` VARCHAR(2) NOT NULL,
    `node` VARCHAR(50) NOT NULL,
    `usage_type` VARCHAR(2) NOT NULL,
    `usage_code` VARCHAR(50) NOT NULL,
    `body_type` VARCHAR(50) NOT NULL,
    `body_name_en` VARCHAR(191) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `mws_prefix` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `registration_type_id` INTEGER NOT NULL,
    `insured_type` VARCHAR(191) NOT NULL,
    `title_code` VARCHAR(191) NOT NULL,
    `description_th` VARCHAR(191) NOT NULL,
    `description_en` VARCHAR(191) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `mws_vmi_vehicle_type` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `vehicle_code` VARCHAR(50) NOT NULL,
    `vehicle_type_th` VARCHAR(50) NOT NULL,
    `vehicle_type_en` VARCHAR(50) NOT NULL,
    `usage_code` VARCHAR(50) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `mws_vmi_vehicle_type_vehicle_code_key`(`vehicle_code`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `mws_vmi_vehicle_usage` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `usage_code` VARCHAR(50) NOT NULL,
    `usage_th` VARCHAR(50) NOT NULL,
    `usage_en` VARCHAR(50) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `occupation` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `payment` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NOT NULL,
    `payment_method_id` INTEGER NOT NULL,
    `deejing_credit_id` INTEGER NULL,
    `payment_reference` VARCHAR(191) NOT NULL,
    `amount` DECIMAL(15, 2) NOT NULL,
    `payment_status` VARCHAR(50) NOT NULL,
    `payment_date` DATE NOT NULL,
    `payment_time` TIME NOT NULL,
    `payment_proof_path` VARCHAR(191) NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `payment_payment_reference_key`(`payment_reference`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `payment_method` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `province` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `code` VARCHAR(50) NOT NULL,
    `name_th` VARCHAR(191) NOT NULL,
    `name_en` VARCHAR(191) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `province_code_key`(`code`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `registration_type` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `code` VARCHAR(50) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `registration_type_code_key`(`code`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `subagent` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NOT NULL,
    `code` VARCHAR(50) NOT NULL,
    `full_name` VARCHAR(191) NOT NULL,
    `phone_number` VARCHAR(20) NOT NULL,
    `password` VARCHAR(191) NOT NULL,
    `email` VARCHAR(191) NOT NULL,
    `card_number` VARCHAR(13) NULL,
    `birthdate` DATE NULL,
    `gender` VARCHAR(10) NULL,
    `address` VARCHAR(191) NULL,
    `sub_district_id` INTEGER NOT NULL,
    `zip_code` VARCHAR(10) NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `subagent_code_key`(`code`),
    UNIQUE INDEX `subagent_email_key`(`email`),
    UNIQUE INDEX `subagent_card_number_key`(`card_number`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `sub_district` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `code` VARCHAR(50) NOT NULL,
    `name_th` VARCHAR(191) NOT NULL,
    `name_en` VARCHAR(191) NULL,
    `latitude` DECIMAL(9, 6) NULL,
    `longitude` DECIMAL(9, 6) NULL,
    `district_id` INTEGER NOT NULL,
    `zipcode` VARCHAR(10) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `sub_district_code_key`(`code`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `permission` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `parent_id` INTEGER NULL DEFAULT 0,
    `code` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `permission_code_key`(`code`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user_role` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `code` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `user_role_code_key`(`code`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user_role_log` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NOT NULL,
    `user_role_id` INTEGER NOT NULL,
    `data` JSON NOT NULL,
    `is_status` BOOLEAN NOT NULL DEFAULT true,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user_role_permission` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `permission_id` INTEGER NOT NULL,
    `user_role_id` INTEGER NOT NULL,
    `is_granted` BOOLEAN NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `is_status` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `user_role_permission_user_role_id_permission_id_key`(`user_role_id`, `permission_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE UNIQUE INDEX `admin_user_id_key` ON `admin`(`user_id`);

-- AddForeignKey
ALTER TABLE `car_insurance` ADD CONSTRAINT `car_insurance_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance` ADD CONSTRAINT `car_insurance_car_brand_id_fkey` FOREIGN KEY (`car_brand_id`) REFERENCES `car_brand`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance` ADD CONSTRAINT `car_insurance_car_model_id_fkey` FOREIGN KEY (`car_model_id`) REFERENCES `car_model`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance` ADD CONSTRAINT `car_insurance_car_submodel_id_fkey` FOREIGN KEY (`car_submodel_id`) REFERENCES `car_submodel`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance` ADD CONSTRAINT `car_insurance_insurance_type_id_fkey` FOREIGN KEY (`insurance_type_id`) REFERENCES `insurance_type`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance` ADD CONSTRAINT `car_insurance_license_plate_province_id_fkey` FOREIGN KEY (`license_plate_province_id`) REFERENCES `license_plate_province`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance` ADD CONSTRAINT `car_insurance_car_code_id_fkey` FOREIGN KEY (`car_code_id`) REFERENCES `car_code`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance` ADD CONSTRAINT `car_insurance_car_insurance_status_id_fkey` FOREIGN KEY (`car_insurance_status_id`) REFERENCES `car_insurance_status`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_submodel` ADD CONSTRAINT `car_submodel_car_brand_id_fkey` FOREIGN KEY (`car_brand_id`) REFERENCES `car_brand`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_submodel` ADD CONSTRAINT `car_submodel_car_model_id_fkey` FOREIGN KEY (`car_model_id`) REFERENCES `car_model`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `agent` ADD CONSTRAINT `agent_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `agent` ADD CONSTRAINT `agent_agent_type_id_fkey` FOREIGN KEY (`agent_type_id`) REFERENCES `agent_type`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `agent` ADD CONSTRAINT `agent_occupation_id_fkey` FOREIGN KEY (`occupation_id`) REFERENCES `occupation`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `agent` ADD CONSTRAINT `agent_insurance_sales_experience_id_fkey` FOREIGN KEY (`insurance_sales_experience_id`) REFERENCES `insurance_sales_experience`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `agent` ADD CONSTRAINT `agent_id_card_sub_district_id_fkey` FOREIGN KEY (`id_card_sub_district_id`) REFERENCES `sub_district`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `agent` ADD CONSTRAINT `agent_delivery_sub_district_id_fkey` FOREIGN KEY (`delivery_sub_district_id`) REFERENCES `sub_district`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `bank_account` ADD CONSTRAINT `bank_account_payment_id_fkey` FOREIGN KEY (`payment_id`) REFERENCES `payment`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_act` ADD CONSTRAINT `car_act_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_act` ADD CONSTRAINT `car_act_insurance_id_fkey` FOREIGN KEY (`insurance_id`) REFERENCES `insurance`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_act` ADD CONSTRAINT `car_act_car_code_id_fkey` FOREIGN KEY (`car_code_id`) REFERENCES `car_code`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_act` ADD CONSTRAINT `car_act_payment_id_fkey` FOREIGN KEY (`payment_id`) REFERENCES `payment`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_act_payment` ADD CONSTRAINT `car_act_payment_car_act_id_fkey` FOREIGN KEY (`car_act_id`) REFERENCES `car_act`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_act_payment` ADD CONSTRAINT `car_act_payment_payment_id_fkey` FOREIGN KEY (`payment_id`) REFERENCES `payment`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_act_policy_detail` ADD CONSTRAINT `car_act_policy_detail_car_act_id_fkey` FOREIGN KEY (`car_act_id`) REFERENCES `car_act`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_act_policy_detail` ADD CONSTRAINT `car_act_policy_detail_license_plate_province_id_fkey` FOREIGN KEY (`license_plate_province_id`) REFERENCES `license_plate_province`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_act_policy_detail` ADD CONSTRAINT `car_act_policy_detail_car_brand_id_fkey` FOREIGN KEY (`car_brand_id`) REFERENCES `car_brand`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_act_policy_detail` ADD CONSTRAINT `car_act_policy_detail_car_model_id_fkey` FOREIGN KEY (`car_model_id`) REFERENCES `car_model`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_act_policy_detail` ADD CONSTRAINT `car_act_policy_detail_color_id_fkey` FOREIGN KEY (`color_id`) REFERENCES `color`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_act_policy_page_detail` ADD CONSTRAINT `car_act_policy_page_detail_car_act_id_fkey` FOREIGN KEY (`car_act_id`) REFERENCES `car_act`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_act_policy_page_detail` ADD CONSTRAINT `car_act_policy_page_detail_province_id_fkey` FOREIGN KEY (`province_id`) REFERENCES `province`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_act_policy_page_detail` ADD CONSTRAINT `car_act_policy_page_detail_prefix_id_fkey` FOREIGN KEY (`prefix_id`) REFERENCES `prefix_name`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_addons` ADD CONSTRAINT `car_insurance_addons_car_insurance_id_fkey` FOREIGN KEY (`car_insurance_id`) REFERENCES `car_insurance`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_addons` ADD CONSTRAINT `car_insurance_addons_insurance_addon_service_id_fkey` FOREIGN KEY (`insurance_addon_service_id`) REFERENCES `insurance_addon_service`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_compare` ADD CONSTRAINT `car_insurance_compare_car_insurance_id_fkey` FOREIGN KEY (`car_insurance_id`) REFERENCES `car_insurance`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_compare` ADD CONSTRAINT `car_insurance_compare_insurance_id_fkey` FOREIGN KEY (`insurance_id`) REFERENCES `insurance`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_compare_addons` ADD CONSTRAINT `car_insurance_compare_addons_car_insurance_compare_id_fkey` FOREIGN KEY (`car_insurance_compare_id`) REFERENCES `car_insurance_compare`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_compare_addons` ADD CONSTRAINT `car_insurance_compare_addons_insurance_addon_service_id_fkey` FOREIGN KEY (`insurance_addon_service_id`) REFERENCES `insurance_addon_service`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_compare_detail` ADD CONSTRAINT `car_insurance_compare_detail_insurance_id_fkey` FOREIGN KEY (`insurance_id`) REFERENCES `insurance`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_compare_detail` ADD CONSTRAINT `car_insurance_compare_detail_insurance_type_id_fkey` FOREIGN KEY (`insurance_type_id`) REFERENCES `insurance_type`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_compare_detail` ADD CONSTRAINT `car_insurance_compare_detail_car_insurance_compare_id_fkey` FOREIGN KEY (`car_insurance_compare_id`) REFERENCES `car_insurance_compare`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_customer` ADD CONSTRAINT `car_insurance_customer_car_insurance_id_fkey` FOREIGN KEY (`car_insurance_id`) REFERENCES `car_insurance`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_customer` ADD CONSTRAINT `car_insurance_customer_registration_type_id_fkey` FOREIGN KEY (`registration_type_id`) REFERENCES `registration_type`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_customer` ADD CONSTRAINT `car_insurance_customer_prefix_name_id_fkey` FOREIGN KEY (`prefix_name_id`) REFERENCES `prefix_name`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_customer` ADD CONSTRAINT `car_insurance_customer_province_id_fkey` FOREIGN KEY (`province_id`) REFERENCES `province`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_customer` ADD CONSTRAINT `car_insurance_customer_business_type_id_fkey` FOREIGN KEY (`business_type_id`) REFERENCES `business_type`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_detail` ADD CONSTRAINT `car_insurance_detail_insurance_id_fkey` FOREIGN KEY (`insurance_id`) REFERENCES `insurance`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_detail` ADD CONSTRAINT `car_insurance_detail_insurance_type_id_fkey` FOREIGN KEY (`insurance_type_id`) REFERENCES `insurance_type`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_detail` ADD CONSTRAINT `car_insurance_detail_car_insurance_id_fkey` FOREIGN KEY (`car_insurance_id`) REFERENCES `car_insurance`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_payment` ADD CONSTRAINT `car_insurance_payment_car_insurance_id_fkey` FOREIGN KEY (`car_insurance_id`) REFERENCES `car_insurance`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_insurance_payment` ADD CONSTRAINT `car_insurance_payment_payment_id_fkey` FOREIGN KEY (`payment_id`) REFERENCES `payment`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `deejing_credit` ADD CONSTRAINT `deejing_credit_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `deejing_credit_transaction_log` ADD CONSTRAINT `deejing_credit_transaction_log_deejing_credit_id_fkey` FOREIGN KEY (`deejing_credit_id`) REFERENCES `deejing_credit`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `district` ADD CONSTRAINT `district_province_id_fkey` FOREIGN KEY (`province_id`) REFERENCES `province`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `marketing_fee_rate_act` ADD CONSTRAINT `marketing_fee_rate_act_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `marketing_fee_rate_act` ADD CONSTRAINT `marketing_fee_rate_act_car_code_id_fkey` FOREIGN KEY (`car_code_id`) REFERENCES `car_code`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `marketing_fee_rate_act` ADD CONSTRAINT `marketing_fee_rate_act_insurance_id_fkey` FOREIGN KEY (`insurance_id`) REFERENCES `insurance`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `marketing_fee_rate_act` ADD CONSTRAINT `marketing_fee_rate_act_marketing_fee_rate_act_type_id_fkey` FOREIGN KEY (`marketing_fee_rate_act_type_id`) REFERENCES `marketing_fee_rate_act_type`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `marketing_fee_rate_insurance` ADD CONSTRAINT `marketing_fee_rate_insurance_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `marketing_fee_rate_insurance` ADD CONSTRAINT `marketing_fee_rate_insurance_insurance_type_id_fkey` FOREIGN KEY (`insurance_type_id`) REFERENCES `insurance_type`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `marketing_fee_rate_insurance` ADD CONSTRAINT `marketing_fee_rate_insurance_insurance_id_fkey` FOREIGN KEY (`insurance_id`) REFERENCES `insurance`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `mws_prefix` ADD CONSTRAINT `mws_prefix_registration_type_id_fkey` FOREIGN KEY (`registration_type_id`) REFERENCES `registration_type`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `payment` ADD CONSTRAINT `payment_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `payment` ADD CONSTRAINT `payment_payment_method_id_fkey` FOREIGN KEY (`payment_method_id`) REFERENCES `payment_method`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `payment` ADD CONSTRAINT `payment_deejing_credit_id_fkey` FOREIGN KEY (`deejing_credit_id`) REFERENCES `deejing_credit`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `subagent` ADD CONSTRAINT `subagent_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `subagent` ADD CONSTRAINT `subagent_sub_district_id_fkey` FOREIGN KEY (`sub_district_id`) REFERENCES `sub_district`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user` ADD CONSTRAINT `user_user_role_id_fkey` FOREIGN KEY (`user_role_id`) REFERENCES `user_role`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_role_log` ADD CONSTRAINT `user_role_log_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `admin`(`user_id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_role_log` ADD CONSTRAINT `user_role_log_user_role_id_fkey` FOREIGN KEY (`user_role_id`) REFERENCES `user_role`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_role_permission` ADD CONSTRAINT `user_role_permission_permission_id_fkey` FOREIGN KEY (`permission_id`) REFERENCES `permission`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_role_permission` ADD CONSTRAINT `user_role_permission_user_role_id_fkey` FOREIGN KEY (`user_role_id`) REFERENCES `user_role`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
