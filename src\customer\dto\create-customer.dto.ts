import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import {
  IsBoolean,
  IsInt,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
} from "class-validator";

export class CreateCustomerDto {
  @IsInt()
  @IsOptional()
  @Transform(({ value }) => Number(value))
  @ApiProperty({
    example: 1,
    description: "The unique car_insurance_id of the customer customer",
    type: Number,
  })
  user_employee_id?: number;

  @IsInt()
  @Transform(({ value }) => Number(value))
  @ApiProperty({
    example: 1,
    description: "The unique registration_type_id of the customer customer",
    type: Number,
  })
  registration_type_id: number;

  @IsInt()
  @Transform(({ value }) => (value === "" ? undefined : Number(value)))
  @IsOptional()
  @ApiProperty({
    example: 1,
    description: "The unique prefix_name_id of the customer customer",
    type: Number,
  })
  prefix_name_id?: number;

  @IsInt()
  @Transform(({ value }) => Number(value))
  @ApiProperty({
    example: 1,
    description: "The unique business_type_id of the customer customer",
    type: Number,
  })
  business_type_id: number;

  @IsInt()
  @Transform(({ value }) => Number(value))
  @ApiProperty({
    example: 1,
    description:
      "The unique license_plate_province_id of the customer customer",
    type: Number,
  })
  license_plate_province_id: number;

  @IsString()
  @ApiProperty({
    example: "**********",
    description: "The phone number of the customer customer",
    type: String,
  })
  phone_number: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "สมชาย",
    description: "The first name of the customer customer",
    type: String,
  })
  first_name?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "คนดี",
    description: "The last name of the customer customer",
    type: String,
  })
  last_name?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "บริษัทคนไทย",
    description: "The business name of the customer customer",
    type: String,
  })
  business_name?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "123/1 ",
    description: "address of the customer customer",
    type: String,
  })
  address?: string;

  @IsString()
  @ApiProperty({
    example: "กขค123",
    description: "car plate number of the customer customer",
    type: String,
  })
  car_registration_number: string;

  @IsBoolean()
  @Transform(({ value }) => value === "true" || value === true)
  @IsOptional()
  @ApiProperty({
    example: 1,
    description: "status active of the customer customer",
    type: Boolean,
  })
  is_active?: boolean;
}

export class PaginateQueryDto {
  @ApiPropertyOptional({ example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number = 1;

  @ApiPropertyOptional({ example: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  perPage?: number = 10;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  searchText?: string = "";
}
