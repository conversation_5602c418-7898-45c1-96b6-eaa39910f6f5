import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsString } from 'class-validator';

export class CreateTestTableDto {
  @ApiProperty({
    description: 'The name of the test.',
    example: 'Test',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Indicates whether the test is active.',
    example: true,
  })
  @IsBoolean()
  is_active: boolean;
}
