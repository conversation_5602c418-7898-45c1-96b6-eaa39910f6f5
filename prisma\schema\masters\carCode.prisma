model CarCode {
    id         Int      @id @default(autoincrement())
    code       String   @db.Var<PERSON>har(50)
    name       String   @db.VarChar(255)
    is_status  Boolean  @default(true)
    is_active  Boolean  @default(true)
    created_at DateTime @default(now())
    updated_at DateTime @updatedAt

    carInsurance CarInsurance[]

    carAct  CarAct[]
    marketingFeeRateAct MarketingFeeRateAct[]
    @@map("car_code")
}
