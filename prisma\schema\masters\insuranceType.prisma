model InsuranceType {
    id         Int      @id @default(autoincrement())
    place_id   Int
    code       String   @unique @db.VarChar(50)
    name       String   @db.VarChar(255)
    is_status  Boolean  @default(true)
    is_active  Boolean  @default(true)
    created_at DateTime @default(now())
    updated_at DateTime @updatedAt

    // Relations
    place Place @relation(fields: [place_id], references: [id], onDelete: Cascade)
    carInsurance  CarInsurance[]
    carInsuranceCompare  CarInsuranceCompare[]
    // carInsuranceCompareDetail  CarInsuranceCompareDetail[]
    carInsuranceDetail  CarInsuranceDetail[]
    marketingFeeRateInsurance MarketingFeeRateInsurance[]
    @@map("insurance_type")
}
