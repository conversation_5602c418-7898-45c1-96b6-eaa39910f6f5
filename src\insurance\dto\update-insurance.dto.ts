import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsBoolean, IsOptional, IsString } from "class-validator";

export class UpdateInsuranceDto {
  @IsString()
  @ApiProperty({
    example: "CO001",
    description: "The unique code of the insurance",
    required: false,
  })
  code?: string;

  @IsString()
  @ApiProperty({
    example: "วิริยะ  ประกันภัย",
    description: "The name of the insurance",
    required: false,
  })
  name?: string;

  @IsBoolean()
  @Transform(({ value }) => {
    const data = value;
    return data.trim() === "true" || value === true;
  })
  @ApiProperty({
    example: true,
    description: "The status of the insurance",
    type: Boolean,
    required: false,
  })
  is_status?: boolean;

  @IsBoolean()
  @Transform(({ value }) => {
    const data = value;
    return data.trim() === "true" || value === true;
  })
  @IsOptional()
  @ApiProperty({
    example: true,
    description: "Indicates whether the insurance is active",
    type: <PERSON>olean,
    required: false,
  })
  is_active?: boolean;

  @IsString()
  @ApiProperty({
    type: "string",
    format: "binary",
    description: "Image file for the insurance",
    required: false,
  })
  image_path?: any;
}
