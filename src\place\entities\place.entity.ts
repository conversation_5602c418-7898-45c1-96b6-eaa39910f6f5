import { ApiProperty } from '@nestjs/swagger';

export class Place {

  @ApiProperty({
    example: 'ซ่อมอู่',
    description: 'The name of the place',
  })
  name: string;

  @ApiProperty({
    example: true,
    description: 'Indicates whether the insurance type is active',
  })
  is_active: boolean;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the insurance type was created',
  })
  created_at: Date;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the insurance type was last updated',
  })
  updated_at: Date;
}
