import { PartialType } from '@nestjs/swagger';
import { CreateCarBrandDto } from './create-car-brand.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsString } from 'class-validator';
import { Transform } from 'class-transformer';
import { Optional } from '@nestjs/common';

export class UpdateCarBrandDto {
  @IsString()
  @ApiProperty({ example: 'TOYOTA', description: 'The unique code of the car brand', required: false })
  code?: string;

  @IsString()
  @ApiProperty({ example: 'Toyota', description: 'The name of the car brand', required: false })
  name?: string;

  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  @ApiProperty({ example: true, description: 'The status of the car brand', type: Boolean, required: false })
  is_status?: boolean;

  @IsBoolean()
  @Optional()
  @Transform(({ value }) => value === 'true' || value === true)
  @ApiProperty({ example: true, description: 'Indicates whether the car brand is active', type: Boolean, required: false })
  is_active?: boolean;

  @IsString()
  @ApiProperty({ type: 'string', format: 'binary', description: 'Image file for the car brand', required: false })
  image_path?: any;
}