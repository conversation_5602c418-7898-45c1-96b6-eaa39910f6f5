import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { Insurance, Prisma } from '@prisma/client';
import { join } from 'path';
import { HandleErrorService } from "@common/services/handle-error.services";

@Injectable()
export class InsuranceService {
  public static MODEL_NAME = 'insurance';
  constructor(private prisma: PrismaService,
    private readonly handleErrorService: HandleErrorService) { }

  async create(data: Prisma.CarBrandCreateInput, file: Express.Multer.File): Promise<Insurance> {
    try {
      const imagePath = file ? join('uploads', InsuranceService.MODEL_NAME, file.filename) : null;
      return await this.prisma.insurance.create({
        data: {
          ...data,
          image_path: imagePath,
        },
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }

  }

  async findAll(): Promise<Insurance[]> {
    return await this.prisma.insurance.findMany();
  }

  async findOne(id: number): Promise<Insurance> {
    return this.prisma.insurance.findUnique({ where: { id, is_active: true } });
  }

  async update(id: number, data: Prisma.InsuranceUpdateInput, file: Express.Multer.File): Promise<Insurance> {
    try {
      const imagePath = file ? join('uploads', InsuranceService.MODEL_NAME, file.filename) : data.image_path;
      return await this.prisma.insurance.update({
        where: { id },
        data: {
          ...data,
          image_path: imagePath,
        },
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }

  }

  async remove(id: number): Promise<Insurance> {
    return this.prisma.insurance.update({
      where: { id },
      data: {
        is_active: false,
      },
    });
  }
}
