import { Module } from '@nestjs/common';
import { LicensePlateProvinceController } from './license-plate-province.controller';
import { LicensePlateProvinceService } from './license-plate-province.service';
import { PrismaModule } from 'src/prisma/prisma.module';
import { PaginationService } from "@common/services/pagination.service";
import { HandleErrorService } from '@common/services/handle-error.services'; 

@Module({
  controllers: [LicensePlateProvinceController],
  providers: [LicensePlateProvinceService,PaginationService,HandleErrorService],
  imports: [PrismaModule],
})
export class LicensePlateProvinceModule {}
