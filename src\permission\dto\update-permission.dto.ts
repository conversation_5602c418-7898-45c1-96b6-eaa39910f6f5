import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsBoolean, IsInt, IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';
import { <PERSON><PERSON><PERSON><PERSON> } from 'src/_decorators/transform-decorator.decorator';
import { UpdatePermissionAccessPriVilegeDto } from 'src/permission-access-privilege/dto/update-permission-access-privilege.dto';

export class UpdatePermissionDto {
  /* @IsInt()
  @IsOptional()
  @ApiProperty({ example: 1, description: 'The parent ID of the permission, if any', type: Number })
  parent_id?: number; */

  /* @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'P0001', description: 'The unique code of the permission' })
  code: string; */
  
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'PER001', description: 'The unique code of the permission' })
  lv_code: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'Manage Users', description: 'The name of the permission' })
  name: string;

  @IsBoolean()
  @ApiProperty({ example: true, description: 'The status of the permission', type: Boolean })
  is_status: boolean;
  
  @IsBoolean()
  @IsOptional()
  @ApiProperty({ example: true, description: 'Indicates whether the permission is active', type: Boolean })
  is_active?: boolean;
  
  @IsInt()
  @IsOptional()
  @ApiProperty({ example: 1, description: 'The unique user_emp_update_id of the permission access privilege', type: Number })
  user_emp_update_id?: number;

  @IsOptional()
  @ValidateNested({ each: true })
  @ParseJson(UpdatePermissionAccessPriVilegeDto)
  @Type(() => UpdatePermissionAccessPriVilegeDto)
  @ApiProperty({ type: UpdatePermissionAccessPriVilegeDto, isArray: true })
  permission_access_items ?: UpdatePermissionAccessPriVilegeDto[];
}