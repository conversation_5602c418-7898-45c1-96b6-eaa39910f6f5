import { FileService } from '@common/services/file.service';
import { HandleErrorService } from "@common/services/handle-error.services";
import { PaginationService } from "@common/services/pagination.service";
import { Module } from "@nestjs/common";
import { HwcFileUploadService } from "src/hwc-file-upload/hwc-file-upload.service";
import { PrismaService } from "src/prisma/prisma.service";
import { AttachdocumentsController } from "./attachdocuments.controller";
import { AttachdocumentsService } from "./attachdocuments.service";

@Module({
  controllers: [AttachdocumentsController],
  providers: [
    AttachdocumentsService,
    FileService,
    PaginationService,
    PrismaService,
    HwcFileUploadService,
    HandleErrorService,
  ],
})
export class AttachdocumentsModule {}
