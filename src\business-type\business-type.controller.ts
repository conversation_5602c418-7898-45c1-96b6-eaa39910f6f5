import { Controller, Get } from "@nestjs/common";
import { BusinessTypeService } from "./business-type.service";
import { ApiOperation, ApiResponse } from "@nestjs/swagger";
import { BusinessType } from "@prisma/client";

@Controller("business-type")
export class BusinessTypeController {
  constructor(private readonly businessTypeService: BusinessTypeService) {}

  @Get()
  @ApiOperation({ summary: "Get business type All" })
  @ApiResponse({
    status: 200,
    description: "business type has been successfully retrieved.",
  })
  findAll(){
    return this.businessTypeService.findAll();
  }
}
