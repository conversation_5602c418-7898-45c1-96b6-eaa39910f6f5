import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  UploadedFile,
  UseInterceptors,
  ParseIntPipe,
  Query,
} from "@nestjs/common";
import { CarModelService } from "./car-model.service";
import { CarModel } from "./entities/car-model.entity";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";
import { FileInterceptor } from "@nestjs/platform-express";
import { diskStorage } from "multer";
import { CreateCarModelDto } from "./dto/create-car-model.dto";
import { UpdateCarModelDto } from "./dto/update-car-model.dto";
import { join } from "path";
import { FileService } from "@common/services/file.service"; // Assuming this is where your utility function is
import { ConvertService } from "@common/services/convert.service";
import { PaginationService } from "@common/services/pagination.service";

@ApiTags("car-model")
@Controller("car-model")
export class CarModelController {
  constructor(
    private readonly carModelService: CarModelService,
    private readonly paginationService: PaginationService,
    private fileService: FileService
  ) { }

  @Post()
  @ApiOperation({ summary: "Create a new car model" })
  @ApiResponse({
    status: 201,
    description: "The car model has been successfully created.",
    type: CreateCarModelDto,
  })
  create(@Body() createCarModelDto: CreateCarModelDto) {
    // Convert string fields to boolean
    /* createCarModelDto.is_status = ConvertService.stringToBoolean(
      createCarModelDto.is_status
    );
    createCarModelDto.is_active = ConvertService.stringToBoolean(
      createCarModelDto.is_active
    ); */

    return this.carModelService.create(createCarModelDto);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update a car model by ID" })
  @ApiResponse({
    status: 200,
    description: "The car model has been successfully updated.",
    type: UpdateCarModelDto,
  })
  async update(
    @Param("id", ParseIntPipe) id: number,
    @Body() updateCarModelDto: UpdateCarModelDto
  ) {
    // Convert string fields to boolean
    updateCarModelDto.is_status = ConvertService.stringToBoolean(
      updateCarModelDto.is_status
    );
    updateCarModelDto.is_active = ConvertService.stringToBoolean(
      updateCarModelDto.is_active
    );
    return this.carModelService.update(id, updateCarModelDto);
  }

  @Get("paginate")
  @ApiOperation({ summary: "Get a list of all car models with pagination" })
  @ApiQuery({ name: "page", required: false, type: Number, example: 1 })
  @ApiQuery({ name: "perPage", required: false, type: Number, example: 10 })
  @ApiQuery({ name: "searchText", required: false, type: String, example: "camry" })
  @ApiResponse({
    status: 200,
    description: "The list of car models has been successfully retrieved.",
    type: [CarModel],
  })
  async pagination(
    @Query("page", ParseIntPipe) page: number = 1,
    @Query("perPage", ParseIntPipe) perPage: number = 10,
    @Query("searchText") searchText: string = ""
  ) {
    return this.paginationService.paginate(
      CarModelService.MODEL_NAME,
      page,
      perPage,
      {
        OR: [
          {name: { contains: searchText.toLowerCase() }},
          {code: { contains: searchText.toLowerCase() }}
        ]
      },
      {
        carBrand: true
      }
    );
  }

  @Get()
  @ApiOperation({ summary: "Get car model All" })
  @ApiResponse({
    status: 200,
    description: "The car model has been successfully retrieved.",
    type: CarModel,
  })
  async findAll(
    @Query('year') year?: string,
    @Query('car_brand_id') car_brand_id?: string
  ): Promise<CarModel[]> {
    return await this.carModelService.findAll({ year, car_brand_id });
  }
  @Get(":id")
  @ApiOperation({ summary: "Get a single car model by ID" })
  @ApiResponse({
    status: 200,
    description: "The car model has been successfully retrieved.",
    type: CarModel,
  })
  findOne(@Param("id", ParseIntPipe) id: number) {
    return this.carModelService.findOne(id);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a car model by ID" })
  @ApiResponse({
    status: 200,
    description: "The car model has been successfully deleted.",
    type: CarModel,
  })
  remove(@Param("id", ParseIntPipe) id: number) {
    return this.carModelService.remove(id);
  }
}
