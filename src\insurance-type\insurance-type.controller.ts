import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  UploadedFile,
  UseInterceptors,
  ParseIntPipe,
  Query,
} from "@nestjs/common";
import { InsuranceTypeService } from "./insurance-type.service";
import { InsuranceType } from "./entities/insurance-type.entity";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";

import { CreateInsuranceTypeDto } from "./dto/create-insurance-type.dto";
import { UpdateInsuranceTypeDto } from "./dto/update-insurance-type.dto";
import { join } from "path";

import { ConvertService } from "@common/services/convert.service";
import { PaginationService } from "@common/services/pagination.service";

@ApiTags("insurance-type")
@Controller("insurance-type")
export class InsuranceTypeController {
  constructor(
    private readonly insuranceTypeService: InsuranceTypeService,
    private readonly paginationService: PaginationService,
  ) { }

  @Post()
  @ApiOperation({ summary: "Create a new insurance type" })
  @ApiResponse({
    status: 201,
    description: "The insurance type has been successfully created.",
    type: CreateInsuranceTypeDto,
  })
  create(@Body() createInsuranceTypeDto: CreateInsuranceTypeDto) {
    // Convert string fields to boolean
    createInsuranceTypeDto.is_status = ConvertService.stringToBoolean(
      createInsuranceTypeDto.is_status
    );
    createInsuranceTypeDto.is_active = ConvertService.stringToBoolean(
      createInsuranceTypeDto.is_active
    );

    return this.insuranceTypeService.create(createInsuranceTypeDto);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update a insurance type by ID" })
  @ApiResponse({
    status: 200,
    description: "The insurance type has been successfully updated.",
    type: UpdateInsuranceTypeDto,
  })
  async update(
    @Param("id", ParseIntPipe) id: number,
    @Body() updateInsuranceTypeDto: UpdateInsuranceTypeDto
  ) {
    // Convert string fields to boolean
    updateInsuranceTypeDto.is_status = ConvertService.stringToBoolean(
      updateInsuranceTypeDto.is_status
    );
    updateInsuranceTypeDto.is_active = ConvertService.stringToBoolean(
      updateInsuranceTypeDto.is_active
    );
    return this.insuranceTypeService.update(id, updateInsuranceTypeDto);
  }

  @Get("paginate")
  @ApiOperation({ summary: "Get a list of all insurance types with pagination" })
  @ApiQuery({ name: "page", required: false, type: Number, example: 1 })
  @ApiQuery({ name: "perPage", required: false, type: Number, example: 10 })
  @ApiQuery({ name: "searchText", required: false, type: String, example: "ชั้น" })
  @ApiResponse({
    status: 200,
    description: "The list of insurance types has been successfully retrieved.",
    type: [InsuranceType],
  })
  async pagination(
    @Query("page", ParseIntPipe) page: number = 1,
    @Query("perPage", ParseIntPipe) perPage: number = 10,
    @Query("searchText") searchText: string = ""
  ) {
    return this.paginationService.paginate(
      InsuranceTypeService.MODEL_NAME,
      page,
      perPage,
      {
        OR: [
          {name: { contains: searchText.toLowerCase() }},
          {code: { contains: searchText.toLowerCase() }}
        ]
      },
      {
        place: true
      }
    );
  }

  @Get()
  @ApiOperation({ summary: "Get insurance type model All" })
  @ApiResponse({
    status: 200,
    description: "The insurance type has been successfully retrieved.",
    type: InsuranceType,
  })
  findAll() {
    return this.insuranceTypeService.findAll();
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a single insurance type by ID" })
  @ApiResponse({
    status: 200,
    description: "The insurance type has been successfully retrieved.",
    type: InsuranceType,
  })
  findOne(@Param("id", ParseIntPipe) id: number) {
    return this.insuranceTypeService.findOne(id);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a insurance type by ID" })
  @ApiResponse({
    status: 200,
    description: "The insurance type has been successfully deleted.",
    type: InsuranceType,
  })
  remove(@Param("id", ParseIntPipe) id: number) {
    return this.insuranceTypeService.remove(id);
  }
}
