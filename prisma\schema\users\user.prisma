model User {
    id           Int      @id @default(autoincrement())
    user_role_id Int
    user_type_id Int
    parent_id    Int?
    username     String   @unique @db.VarChar(100)
    password     String   @db.VarChar(255)
    phone_number String?  @db.VarChar(20)
    email        String   @unique @db.Var<PERSON>har(255)
    is_status    Boolean  @default(true)
    is_active    <PERSON>olean  @default(true)
    created_at   DateTime @default(now())
    updated_at   DateTime @updatedAt

    // Relations
    userRole UserRole @relation(fields: [user_role_id], references: [id], onDelete: Cascade)
    userType UserType @relation(fields: [user_type_id], references: [id], onDelete: Cascade)
    admin Admin[]
    agent Agent[]
    carAct CarAct[]
    deejingCredit DeejingCredit[]
    marketingFeeRateAct MarketingFeeRateAct[]
    marketingFeeRateInsurance MarketingFeeRateInsurance[]
    payment Payment[]
    subagent Subagent[]
      carInsurance           CarInsurance[]
  customer               Customer[]
    @@map("user")
}

