import { PrismaClient } from "@prisma/client";
const prisma = new PrismaClient();

const seedData = [
    { id: 1, name: "ซ่อมอู่" },
    { id: 2, name: "ซ่อมห้าง" },
];

export async function placeSeeder() {
    await Promise.all(
        seedData.map(async (data) =>
        prisma.place.upsert({
            where: { id: data.id },
            update: { name: data.name },
            create: data,
        })
        )
    );

    console.log('placeSeeder-Success')
}
