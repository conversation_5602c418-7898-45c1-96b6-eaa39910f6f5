import { ApiProperty } from "@nestjs/swagger";
import { EnumDriverType } from "@prisma/client";
import { Transform } from "class-transformer";
import {
  IsBoolean,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateIf,
} from "class-validator";

export class CreateCarInsuranceCustomerDto {
  @IsInt()
  @IsOptional()
  @Transform(({ value }) => Number(value))
  @ApiProperty({
    example: 1,
    description: "The unique car_insurance_id of the car insurance customer",
    type: Number,
  })
  car_insurance_id?: number;

  @IsInt()
  @Transform(({ value }) => Number(value))
  @ApiProperty({
    example: 1,
    description:
      "The unique registration_type_id of the car insurance customer",
    type: Number,
  })
  registration_type_id: number;

  @IsInt()
  @Transform(({ value }) => (value === "" ? undefined : Number(value)))
  @IsOptional()
  @ApiProperty({
    example: 1,
    description: "The unique prefix_name_id of the car insurance customer",
    type: Number,
  })
  prefix_name_id?: number;

  @IsInt()
  @IsNotEmpty()
  @Transform(({ value }) => parseInt(value, 10))
  @ApiProperty({
    description: "The unique id_card_type of the car insurance",
    type: Number,
  })
  id_card_type: number;

  @IsInt()
  @Transform(({ value }) => Number(value))
  @ApiProperty({
    example: 1,
    description: "The unique business_type_id of the car insurance customer",
    type: Number,
  })
  business_type_id: number;

  @IsInt()
  @IsNotEmpty()
  @Transform(({ value }) => Number(value))
  @ApiProperty({
    example: 1,
    description:
      "The unique license_plate_province_id of the car insurance customer",
    type: Number,
  })
  license_plate_province_id: number;

  @IsString()
  @ApiProperty({
    example: "**********",
    description: "The phone number of the car insurance customer",
    type: String,
  })
  phone_number: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "สมชาย",
    description: "The first name of the car insurance customer",
    type: String,
  })
  first_name?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "คนดี",
    description: "The last name of the car insurance customer",
    type: String,
  })
  last_name?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "บริษัทคนไทย",
    description: "The business name of the car insurance customer",
    type: String,
  })
  business_name?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "123/1 ",
    description: "address of the car insurance customer",
    type: String,
  })
  address?: string;

  @IsString()
  @ApiProperty({
    example: "กขค123",
    description: "car plate number of the car insurance customer",
    type: String,
  })
  car_registration_number: string;

  @ValidateIf((obj) => obj.driver_type !== null)
  @IsEnum(EnumDriverType)
  @IsOptional() // ให้ undefined ผ่านได้
  @Transform(({ value }) => value?.toUpperCase())
  @ApiProperty({
    example: "DRIVER",
    description: "DRIVER,NO_DRIVER",
    required: false,
    nullable: true,
  })
  driver_type?: EnumDriverType | null;

  @IsBoolean()
  @Transform(({ value }) => value === "true" || value === true)
  @IsOptional()
  @ApiProperty({
    example: 1,
    description: "status active of the car insurance customer",
    type: Boolean,
  })
  is_active?: boolean;
}
