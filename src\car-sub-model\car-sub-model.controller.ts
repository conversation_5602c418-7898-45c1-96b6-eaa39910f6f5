import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  UploadedFile,
  UseInterceptors,
  ParseIntPipe,
  Query,
} from "@nestjs/common";
import { CarSubModelService } from "./car-sub-model.service";
import { CarSubModel } from "./entities/car-sub-model.entity";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";
import { FileInterceptor } from "@nestjs/platform-express";
import { diskStorage } from "multer";
import { CreateCarSubModelDto } from "./dto/create-car-sub-model.dto";
import { UpdateCarSubModelDto } from "./dto/update-car-sub-model.dto";
import { join } from "path";
import { FileService } from "@common/services/file.service"; // Assuming this is where your utility function is
import { ConvertService } from "@common/services/convert.service";
import { PaginationService } from "@common/services/pagination.service";

@ApiTags("car-sub-model")
@Controller("car-sub-model")
export class CarSubModelController {
  constructor(
    private readonly carSubModelService: CarSubModelService,
    private readonly paginationService: PaginationService,
    private fileService: FileService
  ) { }

  @Post()
  @ApiOperation({ summary: "Create a new car sub model" })
  @ApiResponse({
    status: 201,
    description: "The car sub model has been successfully created.",
    type: CreateCarSubModelDto,
  })
  create(@Body() createCarSubModelDto: CreateCarSubModelDto) {
    // Convert string fields to boolean
    createCarSubModelDto.is_status = ConvertService.stringToBoolean(
      createCarSubModelDto.is_status
    );
    createCarSubModelDto.is_active = ConvertService.stringToBoolean(
      createCarSubModelDto.is_active
    );
    // console.log('CREATE-CAR-SUB-MODEL-DTO',createCarSubModelDto);
    return this.carSubModelService.create(createCarSubModelDto);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update a car sub model by ID" })
  @ApiResponse({
    status: 200,
    description: "The car sub model has been successfully updated.",
    type: UpdateCarSubModelDto,
  })
  async update(
    @Param("id", ParseIntPipe) id: number,
    @Body() updateCarSubModelDto: UpdateCarSubModelDto
  ) {
    // Convert string fields to boolean
    updateCarSubModelDto.is_status = ConvertService.stringToBoolean(
      updateCarSubModelDto.is_status
    );
    updateCarSubModelDto.is_active = ConvertService.stringToBoolean(
      updateCarSubModelDto.is_active
    );
    return this.carSubModelService.update(id, updateCarSubModelDto);
  }

  @Get("paginate")
  @ApiOperation({ summary: "Get a list of all car sub models with pagination" })
  @ApiQuery({ name: "page", required: false, type: Number, example: 1 })
  @ApiQuery({ name: "perPage", required: false, type: Number, example: 10 })
  @ApiQuery({ name: "searchText", required: false, type: String, example: "camry" })
  @ApiResponse({
    status: 200,
    description: "The list of car sub models has been successfully retrieved.",
    type: [CarSubModel],
  })
  async pagination(
    @Query("page", ParseIntPipe) page: number = 1,
    @Query("perPage", ParseIntPipe) perPage: number = 10,
    @Query("searchText") searchText: string = ""
  ) {
    return this.paginationService.paginate(
      CarSubModelService.MODEL_NAME,
      page,
      perPage,
      {
        OR: [
          {name: { contains: searchText.toLowerCase() }},
          {code: { contains: searchText.toLowerCase() }},
          {
            carBrand: {
              name: { contains: searchText.toLowerCase() },
            },
          },
          {
            carModel: {
              name: { contains: searchText.toLowerCase() },
            },
          },
        ]
      },
      {
        carBrand: true,
        carModel: true,
      }
    );
  }

  @Get()
  @ApiOperation({ summary: "Get car sub model model All" })
  @ApiResponse({
    status: 200,
    description: "The car sub model has been successfully retrieved.",
    type: CarSubModel,
  })
  async findAll(
    @Query('year') year?: string,
    @Query('car_model_id') car_model_id?: string
  ): Promise<CarSubModel[]> {
    // console.log('sub-model-list',car_model_id);
    return await this.carSubModelService.findAll({ year, car_model_id });
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a single car sub model by ID" })
  @ApiResponse({
    status: 200,
    description: "The car sub model has been successfully retrieved.",
    type: CarSubModel,
  })
  findOne(@Param("id", ParseIntPipe) id: number) {
    return this.carSubModelService.findOne(id);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a car sub model by ID" })
  @ApiResponse({
    status: 200,
    description: "The car sub model has been successfully deleted.",
    type: CarSubModel,
  })
  remove(@Param("id", ParseIntPipe) id: number) {
    return this.carSubModelService.remove(id);
  }
}
