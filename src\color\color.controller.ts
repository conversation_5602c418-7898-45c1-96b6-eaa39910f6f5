
import { ColorService } from './color.service';
import { UpdateColorDto } from './dto/update-color.dto';

import { Controller, Get, Post, Body, Put, Param, Delete, Query, ParseIntPipe } from '@nestjs/common';
import { ConvertService } from "@common/services/convert.service";
import { ApiOkResponse } from '@nestjs/swagger';
import { Color } from './entities/color.entity';
import { Prisma } from '@prisma/client';
import { PaginationService } from "@common/services/pagination.service";
import { CreateColorDto } from "./dto/create-color.dto";

import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";
@ApiTags("color")

@Controller('color')
export class ColorController {
  constructor(
    private readonly colorService: ColorService,
    private readonly paginationService: PaginationService,
  ) { }

  @Get("paginate")
  @ApiOperation({ summary: "Get a list of all color with pagination" })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'perPage', required: false, type: Number, example: 10 })
  @ApiQuery({ name: 'searchText', required: false, type: String, example: 'ขาว' })
  @ApiResponse({
    status: 200,
    description: "The list of color has been successfully retrieved.",
    type: [Color],
  })
  async pagination(
    @Query('page', ParseIntPipe) page: number = 1,
    @Query('perPage', ParseIntPipe) perPage: number = 10,
    @Query('searchText') searchText: string = ""
  ) {
    return this.paginationService.paginate(ColorService.Model, page, perPage, {
      OR: [
        {name: { contains: searchText.toLowerCase() }},
        {code: { contains: searchText.toLowerCase() }}
      ]
    });
  }

  @Post()
  @ApiOperation({ summary: "Create a new color" })
  @ApiConsumes('application/json')
  @ApiResponse({
    status: 201,
    description: "The color has been successfully created.",
    type: CreateColorDto,
  })
  create(@Body() createColorDto: CreateColorDto) {
    return this.colorService.create(createColorDto);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update a  color by ID" })
  @ApiConsumes('application/json')
  @ApiResponse({
    status: 200,
    description: "The  color has been successfully updated.",
    type: UpdateColorDto,
  })
  async update(
    @Param("id", ParseIntPipe) id: number,
    @Body() updateColorDto: UpdateColorDto
  ) {
    /* updateColorDto.is_status = ConvertService.stringToBoolean(
      updateColorDto.is_status
    );
    updateColorDto.is_active = ConvertService.stringToBoolean(
      updateColorDto.is_active
    ); */
    return this.colorService.update(id, updateColorDto);
  }

  @Get("/generate-code")
  @ApiOperation({ summary: "Get a color generate code " })
  @ApiResponse({
    status: 200,
    description: "The color code has been successfully retrieved.",
    // type: ,
  })
  generateCodeNumber() {
    return this.colorService.generateCodeNumber();
  }

  @Get("list")
  @ApiOperation({ summary: "Get color model All" })
  @ApiResponse({
    status: 200,
    description: "The color has been successfully retrieved.",
    type: Color,
  })
  findAll() {
    return this.colorService.findAll();
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a single car color by ID" })
  @ApiResponse({
    status: 200,
    description: "The car color has been successfully retrieved.",
    type: Color,
  })
  findOne(@Param("id", ParseIntPipe) id: number) {
    return this.colorService.findOne(id);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a  color by ID" })
  @ApiResponse({
    status: 200,
    description: "The  color has been successfully deleted.",
    type: Color,
  })
  remove(@Param("id", ParseIntPipe) id: number) {
    return this.colorService.remove(id);
  }
}
