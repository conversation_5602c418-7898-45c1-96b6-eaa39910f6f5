import { ApiProperty } from "@nestjs/swagger";
import { IsDate, IsInt, <PERSON>N<PERSON>ber, IsOptional, IsString } from "class-validator";

export class CreatePaymentDto{
    @IsInt()
    @ApiProperty({ example: 1, description: 'The unique user_id of the payment', type: Number })
    user_id: number;
  
    @IsInt()
    @ApiProperty({ example: 1, description: 'The unique payment_method_id of the payment', type: Number })
    payment_method_id: number;
  
    @IsInt()
    @IsOptional()
    @ApiProperty({ example: 1, description: 'The unique deejing_credit_id of the payment', type: Number })
    deejing_credit_id?: number;
  
    @IsString()
    @ApiProperty({ example: 'PAY001', description: 'The unique payment_reference of the payment' })
    payment_reference: string;
  
    @IsNumber({}, { each: true })
    @ApiProperty({ example: 1000.00, description: 'The amount of the payment', type: Number })
    amount: number;
  
    @IsString()
    @ApiProperty({ example: 'paid', description: 'The payment_status of the payment' })
    payment_status: string;
  
    @IsDate()
    @ApiProperty({ example: '2024-08-22', description: 'The payment_date of the payment', type: Date })
    payment_date: Date;
  
    @IsDate()
    @ApiProperty({ example: '12:34:56', description: 'The payment_time of the payment', type: Date })
    payment_time: Date;
  
    @IsString()
    @ApiProperty({ type: 'string', format: 'binary', description: 'Image file for the payment proof' })
    payment_proof_path: any;    
}