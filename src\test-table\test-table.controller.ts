import { Controller, Get } from '@nestjs/common';
import { TestTableService } from './test-table.service';
import { ApiOkResponse } from '@nestjs/swagger';
import { UpdateTestTableDto } from './dto/update-test-table.dto';

@Controller('test-table')
export class TestTableController {
    constructor(
        private readonly testTableService: TestTableService,
      ) {}
    /* @Get()
    @ApiOkResponse({ type: UpdateTestTableDto })
    async findAll() {
      return await this.testTableService.findAll();
    } */
}
