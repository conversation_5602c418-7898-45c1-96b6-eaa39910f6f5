import { FileService } from '@common/services/file.service';
import { PaginationService } from '@common/services/pagination.service';
import {
  Body,
  Controller,
  Param,
  ParseIntPipe,
  Put,
  UploadedFiles,
  UseInterceptors,
} from "@nestjs/common";
import {
  FileFieldsInterceptor,
  FilesInterceptor,
} from "@nestjs/platform-express";
import {
  ApiConsumes,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { EnumCarCustomerImageType } from "@prisma/client";
import { HwcFileUploadService } from "src/hwc-file-upload/hwc-file-upload.service";
import { AttachdocumentsService } from "./attachdocuments.service";
import { FileCustomers } from "./dto/updatecustomerfile";
import {
  CreatePolicyInformationDto,
  FilesUploadDto,
} from "./dto/upload_attach_documents";
@Controller("attachdocuments")
@ApiTags("attachdocuments")
export class AttachdocumentsController {
  constructor(
    private readonly paginationService: PaginationService,
    private fileService: FileService,
    private readonly hwcFileUploadService: HwcFileUploadService,
    private readonly attachdocumentsService: AttachdocumentsService
  ) {}

  @Put(":id")
  @ApiOperation({ summary: "update" })
  @ApiConsumes("multipart/form-data")
  @ApiResponse({
    status: 201,
    description: "The insurance has been successfully created.",
    type: CreatePolicyInformationDto,
  })
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: "id_passport_file", maxCount: 1 },
      { name: "car_regis_book_file", maxCount: 1 },
      { name: "og_insur_policy_file", maxCount: 1 },
      { name: "vehicle_fit_certi", maxCount: 1 },
      { name: "carCustomerImages.back", maxCount: 1 },
      { name: "carCustomerImages.front", maxCount: 1 },
      { name: "carCustomerImages.left", maxCount: 1 },
      { name: "carCustomerImages.right", maxCount: 1 },
      { name: "carCustomerImages.front_left", maxCount: 1 },
      { name: "carCustomerImages.front_right", maxCount: 1 },
      { name: "carCustomerImages.rear_left", maxCount: 1 },
      { name: "carCustomerImages.rear_right", maxCount: 1 },
      { name: "carCustomerImages.mile", maxCount: 1 },
      { name: "etcImages", maxCount: 20 },
    ]),
    FilesInterceptor
  )
  async update(
    @UploadedFiles()
    files: FilesUploadDto,
    @Param("id", ParseIntPipe) id: number,
    @Body() createPolicyInformationDto: CreatePolicyInformationDto
  ) {
    let fileresult: FileCustomers[] = [];
    const id_passport_file = await this.attachdocumentsService.uploadFile(
      files?.id_passport_file
    );
    const car_regis_book_file = await this.attachdocumentsService.uploadFile(
      files?.car_regis_book_file
    );
    const og_insur_policy_file = await this.attachdocumentsService.uploadFile(
      files?.og_insur_policy_file
    );
    const vehicle_fit_certi = await this.attachdocumentsService.uploadFile(
      files?.vehicle_fit_certi
    );
    createPolicyInformationDto.id_passport_file = id_passport_file ?? null;
    createPolicyInformationDto.car_regis_book_file =
      car_regis_book_file ?? null;
    createPolicyInformationDto.og_insur_policy_file =
      og_insur_policy_file ?? null;
    createPolicyInformationDto.vehicle_fit_certi = vehicle_fit_certi ?? null;
    fileresult.push({
      filepath: await this.attachdocumentsService.uploadFile(
        files?.carCustomerImages?.back
      ),
      type: EnumCarCustomerImageType.BACK,
    });
    fileresult.push({
      filepath: await this.attachdocumentsService.uploadFile(
        files?.carCustomerImages?.front
      ),
      type: EnumCarCustomerImageType.FRONT,
    });
    fileresult.push({
      filepath: await this.attachdocumentsService.uploadFile(
        files?.carCustomerImages?.left
      ),
      type: EnumCarCustomerImageType.LEFT,
    });
    fileresult.push({
      filepath: await this.attachdocumentsService.uploadFile(
        files?.carCustomerImages?.front_left
      ),
      type: EnumCarCustomerImageType.FRONTLEFT,
    });
    fileresult.push({
      filepath: await this.attachdocumentsService.uploadFile(
        files?.carCustomerImages?.front_right
      ),
      type: EnumCarCustomerImageType.FRONTRIGHT,
    });
    fileresult.push({
      filepath: await this.attachdocumentsService.uploadFile(
        files?.carCustomerImages?.rear_left
      ),
      type: EnumCarCustomerImageType.REARLEFT,
    });
    fileresult.push({
      filepath: await this.attachdocumentsService.uploadFile(
        files?.carCustomerImages?.rear_right
      ),
      type: EnumCarCustomerImageType.REARRIGHT,
    });
    fileresult.push({
      filepath: await this.attachdocumentsService.uploadFile(
        files?.carCustomerImages?.mile
      ),
      type: EnumCarCustomerImageType.MILE,
    });
    files?.etcImages?.forEach(async (f) => {
      fileresult.push({
        filepath: await this.attachdocumentsService.uploadFile(f),
        type: EnumCarCustomerImageType.ETC,
      });
    });
    return this.attachdocumentsService.update(
      id,
      fileresult,
      createPolicyInformationDto
    );
  }
}
