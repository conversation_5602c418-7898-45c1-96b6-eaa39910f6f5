import { PrismaClient } from "@prisma/client";
const prisma = new PrismaClient();

const seedData = [
    { id: 1, color: "Hex #008000", name: "ซื้อได้" },
    { id: 2, color: "#808080", name: "หมดอายุ" },
];

export async function carInsuranceStatusSeeder() {
    await Promise.all(
        seedData.map(async (data) =>
        prisma.carInsuranceStatus.upsert({
            where: { id: data.id },
            update: { name: data.name },
            create: data,
        })
        )
    );

    console.log('carInsuranceStatusSeeder-Success')
}
