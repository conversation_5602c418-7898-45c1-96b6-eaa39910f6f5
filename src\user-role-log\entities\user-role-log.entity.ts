import { ApiProperty } from '@nestjs/swagger';

export class User<PERSON>oleLog {
  
  @ApiProperty({ example: 1, description: 'The unique user_id of the UserRoleLog', type: Number })
  user_id: number;

  @ApiProperty({ example: 1, description: 'The unique user_id of the UserRoleLog', type: Number })
  user_role_id: number;


  @ApiProperty({ example: 'M0001', description: 'The data of the UserRoleLog' })
  data: object;

  @ApiProperty({
    example: true,
    description: 'The status of the car model (e.g., active or inactive)',
  })
  is_status: boolean;

  @ApiProperty({
    example: true,
    description: 'Indicates whether the car model is active',
  })
  is_active: boolean;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the car model was created',
  })
  created_at: Date;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the car model was last updated',
  })
  updated_at: Date;
}
