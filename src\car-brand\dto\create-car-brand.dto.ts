import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateCarBrandDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'TOYOTA', description: 'The unique code of the car brand' })
  code: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'Toyota', description: 'The name of the car brand' })
  name: string;

  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  @ApiProperty({ example: true, description: 'The status of the car brand', type: Boolean })
  is_status: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ example: true, description: 'Indicates whether the car brand is active', type: Boolean })
  is_active?: boolean;

  // @Transform(({ value }) => value || 'test.png')
  @IsString()
  @IsOptional()
  @ApiProperty({ type: 'string', format: 'binary', description: 'Image file for the car brand' })
  image_path: string;
}