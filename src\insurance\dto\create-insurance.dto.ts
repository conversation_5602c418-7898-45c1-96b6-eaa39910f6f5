import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateInsuranceDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'CO001', description: 'The unique code of the insurance' })
  code: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'วิริยะ  ประกันภัย', description: 'The name of the insurance' })
  name: string;

  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  @ApiProperty({ example: true, description: 'The status of the insurance', type: Boolean })
  is_status: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ example: true, description: 'Indicates whether the insurance is active', type: Boolean })
  is_active?: boolean;

  @IsString()
  @IsOptional()
  @ApiProperty({ type: 'string', format: 'binary', description: 'Image file for the insurance' })
  image_path: any;
}