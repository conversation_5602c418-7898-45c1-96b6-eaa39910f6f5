import { ApiProperty } from "@nestjs/swagger";
import { EnumDriverType } from "@prisma/client";
import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsString,
} from "class-validator";

export class FilesUploadDto {
  id_passport_file: Express.Multer.File;
  car_regis_book_file: Express.Multer.File;
  og_insur_policy_file: Express.Multer.File;
  vehicle_fit_certi: Express.Multer.File;
  carCustomerImages: carCustomerImages;
  etcImages: Express.Multer.File[];
}

export class carCustomerImages {
  back: Express.Multer.File;
  front: Express.Multer.File;
  left: Express.Multer.File;
  right: Express.Multer.File;
  front_left: Express.Multer.File;
  front_right: Express.Multer.File;
  rear_left: Express.Multer.File;
  rear_right: Express.Multer.File;
  mile: Express.Multer.File;
}

export class CreatePolicyInformationDto {
  @IsNotEmpty()
  @ApiProperty({ example: "", description: "ประกันรถยนต์" })
  car_insurance_id: number;

  @IsNotEmpty()
  @ApiProperty({ example: "", description: "ประเภทจดทะเบียน" })
  registration_type_id: number;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: "", description: "ทะเบียนรถ" })
  car_registration_number: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: "", description: "จังหวัด" })
  license_plate_province_id: number;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: "", description: "หมายเลขตัวถัง" })
  chassis_no: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: "", description: "เลขเครื่องยนต์" })
  engine_no: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: "", description: "วันที่เริ่มคุ้มครอง" })
  coverage_start_date: Date;

  @IsString()
  @ApiProperty({
    example: "",
    description: "วันที่คุ้มครอง พ.ร.บ",
    required: false,
  })
  act_coverage_date?: Date;

  @IsEnum(EnumDriverType)
  @IsNotEmpty()
  @ApiProperty({ example: "", description: "ประเภทผู้ขับขี่ enum" })
  driver_type: EnumDriverType; //ประเภทผู้ขับขี่ enum

  @IsString()
  @ApiProperty({
    example: "",
    description: "ชื่อ-นามสกุล ผู้ขับขี่ 1",
    required: false,
  })
  driver_name_1?: string;

  @IsString()
  @ApiProperty({
    example: "",
    description: "วันเกิดผู้ขับขี่ 1",
    required: false,
  })
  brith_date_driver_1?: string;

  @IsString()
  @ApiProperty({
    example: "",
    description: "ชื่อ-นามสกุล ผู้ขับขี่ 2",
    required: false,
  })
  driver_name_2?: string;

  @IsDate()
  @ApiProperty({
    example: "",
    description: "วันเกิดผู้ขับขี่ 2",
    required: false,
  })
  brith_date_driver_2?: Date;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: "", description: "ผู้รับประโยชน์" })
  beneficiary_type: string;

  @IsInt()
  @ApiProperty({
    example: "",
    description: "คำนำหน้าผู้รับประโยชน์",
    required: false,
  })
  beneficiary_prefix_name_id?: number;

  @IsString()
  @ApiProperty({
    example: "",
    description: "ชื่อผู้รับประโยชน์",
    required: false,
  })
  beneficiary_first_name?: string;

  @IsString()
  @ApiProperty({
    example: "",
    description: "นามสกุลผู้รับประโยชน์",
    required: false,
  })
  beneficiary_last_name?: string;

  //ข้อมูลลูกค้า

  @IsInt()
  @ApiProperty({
    example: "",
    description: "ข้อมูลลูกค้า_คำนำหน้า",
    required: false,
  })
  prefix_name_id?: number;

  @IsString()
  @ApiProperty({
    example: "",
    description: "ข้อมูลลูกค้า_ชื่อ",
    required: false,
  })
  first_name?: string;

  @IsString()
  @ApiProperty({
    example: "",
    description: "ข้อมูลลูกค้า_นามสกุล",
    required: false,
  })
  last_name?: string;

  @IsString()
  @ApiProperty({
    example: "",
    description: "ข้อมูลลูกค้า_นามสกุล",
    required: false,
  })
  business_name?: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    example: "",
    description: "ข้อมูลลูกค้า_ประเภทของเลขที่บัตรประจำตัว",
  })
  id_card_type: number;

  @IsString()
  @ApiProperty({
    example: "",
    description: "ข้อมูลลูกค้า_เลขบัตรประจำตัวประชาชน",
    required: false,
  })
  id_card_no?: string;

  @IsString()
  @ApiProperty({
    example: "",
    description: "ข้อมูลลูกค้า_เลขหนังสือเดินทาง",
    required: false,
  })
  passport_no?: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: "", description: "ข้อมูลลูกค้า_เบอร์โทรศัพท์" })
  phone_number: string;

  @IsString()
  @ApiProperty({ example: "", description: "ข้อมูลลูกค้า_ประเภทธุรกิจ" })
  business_type_id: number;

  @IsString()
  @ApiProperty({ example: "", description: "ข้อมูลลูกค้า_ชื่อธุรกิจ" })
  bussiness_name: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: "", description: "ข้อมูลลูกค้า_วันเกิด" })
  birth_date: Date;

  //ที่อยู่หน้ากรมธรรม์
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: "", description: "ที่อยู่หน้ากรมธรรม์_บ้านเลขที่" })
  policy_house_no: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: "", description: "ที่อยู่หน้ากรมธรรม์_ที่อยู่" })
  address: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: "", description: "ที่อยู่หน้ากรมธรรม์_แขวง/ตำบล" })
  sub_district: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: "", description: "ที่อยู่หน้ากรมธรรม์_เขต/อำเภอ" })
  district: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: "", description: "ที่อยู่หน้ากรมธรรม์_จังหวัด" })
  province: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    example: "",
    description: "ที่อยู่หน้ากรมธรรม์_รหัสไปรษณีย์",
  })
  zip_code: string;

  @IsNotEmpty()
  @ApiProperty({
    example: "",
    description: "ประเภทที่อยู่จัดส่งกรมธรรม์",
  })
  policy_address_type: number;

  @ApiProperty({
    type: "number",
    required: false,
    format: "binary",
    description: "รหัสตัวแทนกรณึเลือกที่อยู่ตัวแทน",
  })
  user_emplyee_id?: number;

  @IsString()
  @ApiProperty({ example: "", description: "ที่อยู่จัดส่งกรมธรรม์_บ้านเลขที่" })
  delivery_policy_house_no: string;

  @IsString()
  @ApiProperty({ example: "", description: "ที่อยู่จัดส่งกรมธรรม์_ที่อยู่" })
  delivery_address: string;

  @IsString()
  @ApiProperty({ example: "", description: "ที่อยู่จัดส่งกรมธรรม์_แขวง/ตำบล" })
  delivery_sub_district: string;

  @IsString()
  @ApiProperty({ example: "", description: "ที่อยู่จัดส่งกรมธรรม์_เขต/อำเภอ" })
  delivery_district: string;

  @IsString()
  @ApiProperty({ example: "", description: "ที่อยู่จัดส่งกรมธรรม์_จังหวัด" })
  delivery_province: string;

  @IsString()
  @ApiProperty({
    example: "",
    description: "ที่อยู่จัดส่งกรมธรรม์_รหัสไปรษณีย์",
  })
  delivery_zip_code: string;

  @IsBoolean()
  @ApiProperty({
    example: "",
    description: "ยืนยันการตรวจสอบข้อมูล",
  })
  agree: boolean;

  @IsNotEmpty()
  @ApiProperty({
    type: "string",
    format: "binary",
    description: "สำเนาบัตรประชาชน/หนังสือเดินทาง",
  })
  id_passport_file?: string;
  @IsNotEmpty()
  @ApiProperty({
    type: "string",
    format: "binary",
    description: "เล่มทะเบียนรถ",
  })
  car_regis_book_file?: string;

  @ApiProperty({
    type: "string",
    format: "binary",
    required: false,
    description: "กรมธรรม์ที่ใช้อยู่ฉบับเดิม",
  })
  og_insur_policy_file?: string;
  @IsNotEmpty()
  @ApiProperty({
    type: "string",
    format: "binary",

    description: "ฟอร์มตรวจสภาพรถ",
  })
  vehicle_fit_certi?: string;
  @IsNotEmpty()
  @ApiProperty({
    type: "string",
    format: "binary",

    description: "ด้านหลัง",
  })
  ["carCustomerImages.back"]?: string;
  @IsNotEmpty()
  @ApiProperty({
    type: "string",
    format: "binary",

    description: "ด้านหน้า",
  })
  ["carCustomerImages.front"]?: string;
  @IsNotEmpty()
  @ApiProperty({
    type: "string",
    format: "binary",

    description: "ด้านซ้าย",
  })
  ["carCustomerImages.left"]?: string;
  @IsNotEmpty()
  @ApiProperty({
    type: "string",
    format: "binary",

    description: "ด้านขวา",
  })
  ["carCustomerImages.right"]?: any;
  @IsNotEmpty()
  @ApiProperty({
    type: "string",
    format: "binary",

    description: "ด้านเฉียงซ้ายหน้า",
  })
  ["carCustomerImages.front_left"]?: string;
  @IsNotEmpty()
  @ApiProperty({
    type: "string",
    format: "binary",

    description: "ด้านเฉียงขวาหน้า",
  })
  ["carCustomerImages.front_right"]?: string;
  @IsNotEmpty()
  @ApiProperty({
    type: "string",
    format: "binary",

    description: "ด้านเฉียงซ้ายหลัง",
  })
  ["carCustomerImages.rear_left"]?: string;
  @IsNotEmpty()
  @ApiProperty({
    type: "string",
    format: "binary",

    description: "ด้านเฉียงขวาหลัง",
  })
  ["carCustomerImages.rear_right"]?: string;
  @IsNotEmpty()
  @ApiProperty({
    type: "string",
    format: "binary",

    description: "เลขไมล์รถ",
  })
  ["carCustomerImages.mile"]?: string;

  @ApiProperty({
    type: "string",
    format: "binary",
    required: false,
    isArray: true,
    description: "อื่นๆ",
  })
  etcImages?: string[];
}