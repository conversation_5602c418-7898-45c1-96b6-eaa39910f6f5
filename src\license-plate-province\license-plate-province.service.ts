import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { Prisma } from '@prisma/client';
import { LicensePlateProvince } from './entities/license-plate-province.entity';
import { HandleErrorService } from "@common/services/handle-error.services";
@Injectable()
export class LicensePlateProvinceService {
  public static Model = 'licensePlateProvince';
  constructor(private prisma: PrismaService,
    private readonly handleErrorService: HandleErrorService,) { }

  async findAll(): Promise<LicensePlateProvince[]> {
    return await this.prisma.licensePlateProvince.findMany(
      {
        where: 
        {
          is_active: true
        }
      }
    );
  }

  async findOne(id: number): Promise<LicensePlateProvince> {
    return this.prisma.licensePlateProvince.findUnique({ where: { id, is_active: true } });
  }

  async create(data: Prisma.LicensePlateProvinceCreateInput): Promise<LicensePlateProvince> {
    try {
      return await this.prisma.licensePlateProvince.create({
        data,
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }
  }

  async update(id: number, data: Prisma.LicensePlateProvinceUpdateInput) {
    try {
      return await this.prisma.licensePlateProvince.update({
        where: { id },
        data,
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }
  }

  async remove(id: number): Promise<LicensePlateProvince> {
    const existData = await this.prisma.licensePlateProvince.findUnique({ where: { id, is_active: true } });
    const date = Date.now();
    return this.prisma.licensePlateProvince.update({
      where: { id },
      data: {
        code: date+'-delete-'+existData.code,
        is_active: false,
      },
    });
  }

  async generateCodeNumber() {
    const prefix = "P";

    const lastRecord = await this.prisma.licensePlateProvince.findFirst({
      where: {
        code: {
          startsWith: prefix,
        },
      },
      orderBy: {
        code: "desc",
      },
    });

    const lastNumber = lastRecord
      ? parseInt(lastRecord.code.replace(prefix, ""), 10)
      : 0;
    const newNumber = String(lastNumber + 1).padStart(3, "0");

    return `${prefix}${newNumber}`;
  }
}

