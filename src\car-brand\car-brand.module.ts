import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { CarBrandController } from "./car-brand.controller";
import { CarBrandService } from "./car-brand.service";
import { PrismaModule } from "src/prisma/prisma.module";
import { FileService } from "@common/services/file.service";
import { PaginationService } from "@common/services/pagination.service";
import { HandleErrorService } from '@common/services/handle-error.services'; 
import { MulterModule } from "@nestjs/platform-express";
import { HwcFileUploadService } from "src/hwc-file-upload/hwc-file-upload.service";

@Module({
  controllers: [CarBrandController],
  providers: [CarBrandService, FileService, PaginationService, HandleErrorService, HwcFileUploadService],
  imports: [PrismaModule, MulterModule.register()],
})
export class CarBrandModule {}
