import { PrismaClient } from "@prisma/client";
const prisma = new PrismaClient();

const seedData = [
    { id: 1, name_th: "บุคคลธรรมดา" },
    { id: 2, name_th: "บริษัทจำกัด" },
    { id: 3, name_th: "บริษัทมหาชนจำกัด" },
    { id: 4, name_th: "ห้างหุ้นส่วนสามัญ" },
    { id: 5, name_th: "ห้างหุ้นส่วนจำกัด" },
    { id: 6, name_th: "ร้านค้า" },
    { id: 7, name_th: "สถาบันการศึกษา" },
    { id: 8, name_th: "รัฐวิสาหกิจ" },
    { id: 9, name_th: "ภาครัฐ" },
    { id: 10, name_th: "คณะบุคคล" },
    { id: 11, name_th: "สโมสร" },
    { id: 12, name_th: "กองทุน" },
    { id: 13, name_th: "มูลนิธิ" },
    { id: 14, name_th: "วัด" },
    { id: 15, name_th: "สำนักสงฆ์" },
    { id: 16, name_th: "อื่นๆ" },
];

export async function businessTypeSeeder() {
    await Promise.all(
        seedData.map(async (data) =>
        prisma.businessType.upsert({
            where: { id: data.id },
            update: { name_th: data.name_th },
            create: data,
        })
        )
    );

    console.log('businessTypeSeeder-Success')
}
