import { ApiProperty } from '@nestjs/swagger';

export class LicensePlateProvince {
  @ApiProperty({
    example: 1,
    description: 'The unique identifier of the license plate province',
  })
  id: number;

  @ApiProperty({
    example: 'กก1111',
    description: 'The unique code of the license plate province',
  })
  code: string;

  @ApiProperty({
    example: 'กำแพงเพชร',
    description: 'The name of the license plate province',
  })
  name: string;


  @ApiProperty({
    example: true,
    description: 'The status of the license plate province (e.g., active or inactive)',
  })
  is_status: boolean;

  @ApiProperty({
    example: true,
    description: 'Indicates whether the license plate province is active',
  })
  is_active: boolean;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the license plate province was created',
  })
  created_at: Date;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the license plate province was last updated',
  })
  updated_at: Date;
}
