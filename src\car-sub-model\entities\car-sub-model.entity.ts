import { ApiProperty } from '@nestjs/swagger';

export class CarSubModel {
  @ApiProperty({
    example: 1,
    description: 'The unique identifier of the car sub model',
  })
  id: number;

  @ApiProperty({
    example: '1',
    description: 'The unique car_brand_id of the car sub model',
    type: Number
  })
  car_brand_id: number;

  @ApiProperty({
    example: '1',
    description: 'The unique car_model_id of the car sub model',
    type: Number
  })
  car_model_id: number;

  @ApiProperty({
    example: 'M0001',
    description: 'The unique code of the car sub model',
  })
  code: string;

  @ApiProperty({
    example: 'Camry',
    description: 'The name of the car sub model',
  })
  name: string;

  @ApiProperty({
    example: '2019',
    description: 'The name of the car sub model',
  })
  year: number;

  @ApiProperty({
    example: true,
    description: 'The status of the car sub model (e.g., active or inactive)',
  })
  is_status: boolean;

  @ApiProperty({
    example: true,
    description: 'Indicates whether the car sub model is active',
  })
  is_active?: boolean;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the car sub model was created',
  })
  created_at: Date;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the car sub model was last updated',
  })
  updated_at: Date;
}
