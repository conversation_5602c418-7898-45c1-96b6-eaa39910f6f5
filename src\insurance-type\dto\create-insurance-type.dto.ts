import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { InsuranceType } from '../entities/insurance-type.entity';

export class CreateInsuranceTypeDto {
  @IsInt()
  @ApiProperty({ example: 1, description: 'The unique place_id of the insurance type', type: Number })
  place_id: number;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'T0001', description: 'The unique code of the insurance type' })
  code: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'ชั้น 1', description: 'The name of the insurance type' })
  name: string;

  @IsBoolean()
  @ApiProperty({ example: true, description: 'The status of the insurance type', type: Boolean })
  is_status: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ example: true, description: 'Indicates whether the insurance type is active', type: Boolean })
  is_active?: boolean;

}