import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateCarCodeDto {
  @ApiProperty({
    description: 'The name of the test.',
    example: 'รถยนต์ส่วนบุคคล-ส่วนบุคคล',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    example: '110',
    description: ' code of the  car code',
  })
  @IsString()
  code: string;

  @ApiProperty({
    description: 'Is status.',
    example: true,
  })
  @IsBoolean()
  is_status: boolean;

  @ApiProperty({
    description: 'Indicates whether the test is active.',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  is_active?: boolean;
}
