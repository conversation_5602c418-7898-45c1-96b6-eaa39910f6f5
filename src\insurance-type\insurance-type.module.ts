import { <PERSON>du<PERSON> } from "@nestjs/common";
import { InsuranceTypeController } from "./insurance-type.controller";
import { InsuranceTypeService } from "./insurance-type.service";
import { PrismaModule } from "src/prisma/prisma.module";
import { FileService } from "@common/services/file.service";
import { PaginationService } from "@common/services/pagination.service";
import { HandleErrorService } from '@common/services/handle-error.services'; 

@Module({
  controllers: [InsuranceTypeController],
  providers: [InsuranceTypeService, FileService, PaginationService,HandleErrorService],
  imports: [PrismaModule],
})
export class InsuranceTypeModule {}
