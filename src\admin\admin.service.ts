import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { Admin } from './entities/admin.entity';

import { join } from 'path';
import { HandleErrorService } from "@common/services/handle-error.services";
import { CreateAdminDto } from "./dto/create-admin.dto";

@Injectable()
export class AdminService {
  public static MODEL_NAME = 'admin';
  constructor(
    private prisma: PrismaService,
    private readonly handleErrorService: HandleErrorService) { }

  async create(prisma:any,data: CreateAdminDto ,file: Express.Multer.File): Promise<Admin> {
    try {
      const imagePath = file ? join('uploads', AdminService.MODEL_NAME, file.filename) : null;

      return await prisma.admin.create({
        data: {
          ...data,
          image_path: imagePath,
        },
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }

  }
  async update(prisma: any, userId: number, data: CreateAdminDto, file: Express.Multer.File): Promise<Admin> {
    try {
      const imagePath = file ? join('uploads', AdminService.MODEL_NAME, file.filename) : data.image_path;
  
      return await prisma.admin.update({
        where: { user_id: userId },
        data: {
          ...data,
          image_path: imagePath,
        },
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }
  }
  
  async findOne(id: number): Promise<Admin> {
    return this.prisma.admin.findUnique({ where: { id, is_active: true }, include: { prefix_name: true }, });
  }
}
