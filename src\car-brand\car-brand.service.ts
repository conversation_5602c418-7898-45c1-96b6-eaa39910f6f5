import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { CarBrand } from './entities/car-brand.entity';
import { Prisma } from '@prisma/client';
import { join } from 'path';
import { HandleErrorService } from "@common/services/handle-error.services";

@Injectable()
export class CarBrandService {
  public static MODEL_NAME = 'carBrand';
  constructor(private prisma: PrismaService,
    private readonly handleErrorService: HandleErrorService) { }

  async create(data: Prisma.CarBrandCreateInput): Promise<CarBrand> {
    try {
      return await this.prisma.carBrand.create({
        data: {
          ...data,
        },
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }

  }

  async findAll(): Promise<CarBrand[]> {
    return await this.prisma.carBrand.findMany({
      where: {
        is_active: true
      },
    });
  }

  async findOne(id: number): Promise<CarBrand> {
    return this.prisma.carBrand.findUnique({ where: { id, is_active: true } });
  }

  async update(id: number, data: Prisma.CarBrandUpdateInput): Promise<CarBrand> {
    try {
      return await this.prisma.carBrand.update({
        where: { id },
        data: {
          ...data,
        },
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }

  }

  async remove(id: number): Promise<CarBrand> {
    const existData = await this.prisma.carCode.findUnique({ where: { id, is_active: true } });
    const date = Date.now();
    return this.prisma.carBrand.update({
      where: { id },
      data: {
        code: date+'-delete-'+existData.code,
        is_active: false,
      },
    });
  }
}
