import { Modu<PERSON> } from "@nestjs/common";
import { InsuranceAddonServiceController } from "./insurance-addon-service.controller";
import { InsuranceAddonServiceService } from "./insurance-addon-service.service";
import { PrismaModule } from "src/prisma/prisma.module";
import { FileService } from "@common/services/file.service";
import { PaginationService } from "@common/services/pagination.service";
import { HandleErrorService } from '@common/services/handle-error.services';

@Module({
  controllers: [InsuranceAddonServiceController],
  providers: [InsuranceAddonServiceService, FileService, PaginationService, HandleErrorService],
  imports: [PrismaModule],
})
export class InsuranceAddonServiceModule { }
