import { PartialType } from '@nestjs/swagger';
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';
import { InsuranceType } from '../entities/insurance-type.entity';
import { CreateInsuranceTypeDto } from './create-insurance-type.dto';

/* export class UpdateInsuranceTypeDto {
  @IsInt()
  @ApiProperty({ example: 1, description: 'The unique place_id of the insurance type', type: Number })
  place_id: number;

  @IsString()
  @ApiProperty({ example: 'T0001', description: 'The unique code of the insurance type', required: false })
  code?: string;

  @IsString()
  @ApiProperty({ example: 'ชั้น 1', description: 'The name of the insurance type', required: false })
  name?: string;

  @IsBoolean()
  @ApiProperty({ example: true, description: 'The status of the insurance type', type: Boolean, required: false })
  is_status?: boolean;

  @IsBoolean()
  @ApiProperty({ example: true, description: 'Indicates whether the insurance type is active', type: Boolean, required: false })
  is_active?: boolean;
} */
  export class UpdateInsuranceTypeDto extends PartialType(
    CreateInsuranceTypeDto,
) {}