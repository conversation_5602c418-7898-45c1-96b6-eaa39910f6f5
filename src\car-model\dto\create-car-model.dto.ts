import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { CarModel } from '../entities/car-model.entity';
import { Optional } from '@nestjs/common';

export class CreateCarModelDto {
  @IsInt()
  @ApiProperty({ example: 1, description: 'The unique car_brand_id of the car model', type: Number })
  car_brand_id: number;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'M0001', description: 'The unique code of the car model' })
  code: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'Camry', description: 'The name of the car model' })
  name: string;

  @IsInt()
  @IsNotEmpty()
  @ApiProperty({ example: '2019', description: 'The year of the car model' })
  year: number;

  @IsBoolean()
  @ApiProperty({ example: true, description: 'The status of the car model', type: <PERSON>olean })
  is_status: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ example: true, description: 'Indicates whether the car model is active', type: Bo<PERSON>an })
  is_active?: boolean;
}