import { ApiProperty } from '@nestjs/swagger';

export class InsuranceType {

  @ApiProperty({
    example: '1',
    description: 'The unique place id of the insurance type',
    type: Number
  })
  place_id: number;

  @ApiProperty({
    example: 'T0001',
    description: 'The unique code of the insurance type',
  })
  code: string;

  @ApiProperty({
    example: 'ชั้น 1',
    description: 'The name of the insurance type',
  })
  name: string;

  @ApiProperty({
    example: '2019',
    description: 'The name of the insurance type',
  })
  year: string;

  @ApiProperty({
    example: true,
    description: 'The status of the insurance type (e.g., active or inactive)',
  })
  is_status: boolean;

  @ApiProperty({
    example: true,
    description: 'Indicates whether the insurance type is active',
  })
  is_active: boolean;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the insurance type was created',
  })
  created_at: Date;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the insurance type was last updated',
  })
  updated_at: Date;
}
