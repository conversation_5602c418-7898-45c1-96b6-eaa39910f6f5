import { PartialType } from '@nestjs/swagger';
import { CreateLicensePlateProvinceDto } from './create-license-plate-province.dto';
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateLicensePlateProvinceDto extends PartialType(CreateLicensePlateProvinceDto) {

  @ApiProperty({
    example: 'P0001',
    description: 'The unique code of the  license plate province',
  })
  @IsString()
  code: string;

  @ApiProperty({
    example: 'กรุงเทพมหานคร',
    description: 'The name of the  license plate province',
  })
  @IsString()
  name: string;

  @ApiProperty({
    example: true,
    description: 'The status of the  license plate province (e.g., active or inactive)',
  })
  @IsBoolean()
  is_status: boolean;

  @ApiProperty({
    example: true,
    description: 'Indicates whether the  license plate province is active',
  })
  @IsBoolean()
  @IsOptional()
  is_active?: boolean;
}
