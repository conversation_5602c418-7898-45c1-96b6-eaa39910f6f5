import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { CarModelController } from "./car-model.controller";
import { CarModelService } from "./car-model.service";
import { PrismaModule } from "src/prisma/prisma.module";
import { FileService } from "@common/services/file.service";
import { PaginationService } from "@common/services/pagination.service";
import { HandleErrorService } from '@common/services/handle-error.services'; 

@Module({
  controllers: [CarModelController],
  providers: [CarModelService, FileService, PaginationService,HandleErrorService],
  imports: [PrismaModule],
})
export class CarModelModule {}
