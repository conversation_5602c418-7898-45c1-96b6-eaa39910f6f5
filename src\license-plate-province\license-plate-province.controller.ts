import { Controller, Get, Post, Body, Put, Param, Delete, Query, ParseIntPipe } from '@nestjs/common';
import { LicensePlateProvinceService } from './license-plate-province.service';
import { ApiOkResponse } from '@nestjs/swagger';
import { UpdateLicensePlateProvinceDto } from './dto/update-license-plate-province.dto';
import { LicensePlateProvince } from './entities/license-plate-province.entity';
import { Prisma } from '@prisma/client';
import { PaginationService } from "@common/services/pagination.service";
import { CreateLicensePlateProvinceDto } from "./dto/create-license-plate-province.dto";

import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";
import { ConvertService } from '@common/services/convert.service';
@ApiTags("license-plate-province")
@Controller('license-plate-province')
export class LicensePlateProvinceController {
  constructor(
    private readonly LicensePlateProvinceService: LicensePlateProvinceService,
    private readonly paginationService: PaginationService,
  ) { }
  @Get("/generate-code")
  @ApiOperation({ summary: "Get a license plate province generate code " })
  @ApiResponse({
    status: 200,
    description: "The license plate province code has been successfully retrieved.",
    // type: ,
  })
  generateCodeNumber() {
    return this.LicensePlateProvinceService.generateCodeNumber();
  }

  @Get("paginate")
  @ApiOperation({ summary: "Get a list of all license plate province with pagination" })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'perPage', required: false, type: Number, example: 10 })
  @ApiQuery({ name: 'searchText', required: false, type: String, example: 'กรุงเทพ' })
  @ApiResponse({
    status: 200,
    description: "The list of license plate province has been successfully retrieved.",
    type: [LicensePlateProvince],
  })
  async pagination(
    @Query('page', ParseIntPipe) page: number = 1,
    @Query('perPage', ParseIntPipe) perPage: number = 10,
    @Query('searchText') searchText: string = ""
  ) {
    return this.paginationService.paginate(LicensePlateProvinceService.Model, page, perPage, {
      OR: [
        {name: { contains: searchText.toLowerCase() }},
        {code: { contains: searchText.toLowerCase() }}
      ]
    });
  }

  @Post()
  @ApiOperation({ summary: "Create a new license plate province" })
  @ApiConsumes('application/json')
  @ApiResponse({
    status: 201,
    description: "The license plate province has been successfully created.",
    type: CreateLicensePlateProvinceDto,
  })
  async create(@Body() createLicensePlateProvinceDto: CreateLicensePlateProvinceDto) {
    createLicensePlateProvinceDto.is_status = ConvertService.stringToBoolean(
      createLicensePlateProvinceDto.is_status
    );
    return this.LicensePlateProvinceService.create(createLicensePlateProvinceDto);
  }


  @Put(":id")
  @ApiOperation({ summary: "Update a  license plate province by ID" })
  @ApiConsumes('application/json')
  @ApiResponse({
    status: 200,
    description: "The  license plate province has been successfully updated.",
    type: UpdateLicensePlateProvinceDto,
  })

  async update(
    @Param("id", ParseIntPipe) id: number,
    @Body() updateLicensePlateProvinceDto: UpdateLicensePlateProvinceDto
  ) {
    updateLicensePlateProvinceDto.is_status = ConvertService.stringToBoolean(
      updateLicensePlateProvinceDto.is_status
    );
    return this.LicensePlateProvinceService.update(id, updateLicensePlateProvinceDto);
  }

  @Get()
  @ApiOperation({ summary: "Get insurance type model All" })
  @ApiResponse({
    status: 200,
    description: "The insurance type has been successfully retrieved.",
    type: LicensePlateProvince,
  })
  findAll() {
    return this.LicensePlateProvinceService.findAll();
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a single license plate province by ID" })
  @ApiResponse({
    status: 200,
    description: "The license plate province has been successfully retrieved.",
    type: LicensePlateProvince,
  })
  findOne(@Param("id", ParseIntPipe) id: number) {
    return this.LicensePlateProvinceService.findOne(id);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a license plate province by ID" })
  @ApiResponse({
    status: 200,
    description: "The license plate province has been successfully deleted.",
    type: LicensePlateProvince,
  })
  remove(@Param("id", ParseIntPipe) id: number) {
    return this.LicensePlateProvinceService.remove(id);
  }
}
