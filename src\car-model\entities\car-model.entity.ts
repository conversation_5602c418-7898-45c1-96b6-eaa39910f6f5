import { ApiProperty } from '@nestjs/swagger';

export class CarModel {
  @ApiProperty({
    example: 1,
    description: 'The unique identifier of the car model',
  })
  id: number;

  @ApiProperty({
    example: '1',
    description: 'The unique car_brand_id of the car model',
    type:Number
  })
  car_brand_id: number;

  @ApiProperty({
    example: 'M0001',
    description: 'The unique code of the car model',
  })
  code: string;

  @ApiProperty({
    example: 'Camry',
    description: 'The name of the car model',
  })
  name: string;

  @ApiProperty({
    example: '2019',
    description: 'The name of the car model',
  })
  year: number;

  @ApiProperty({
    example: true,
    description: 'The status of the car model (e.g., active or inactive)',
  })
  is_status: boolean;

  @ApiProperty({
    example: true,
    description: 'Indicates whether the car model is active',
  })
  is_active?: boolean;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the car model was created',
  })
  created_at: Date;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the car model was last updated',
  })
  updated_at: Date;
}
