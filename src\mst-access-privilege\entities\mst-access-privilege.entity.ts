import { ApiProperty } from "@nestjs/swagger";

export class MstAccessPrivilege{    
    @ApiProperty({
    example: 1,
    description: 'The unique identifier of the mst access privilege',
    })
    id: number;

    @ApiProperty({
    example: 'P0001',
    description: 'The unique code of the mst access privilege',
    })
    lv_code: string;

    @ApiProperty({
    example: 'Manage Users',
    description: 'The name of the mst access privilege',
    })
    name: string;

    @ApiProperty({
    example: true,
    description: 'Indicates whether the mst access privilege is status',
    })
    is_status: boolean;

    @ApiProperty({
    example: true,
    description: 'Indicates whether the mst access privilege is active',
    })
    is_active: boolean;

    @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the mst access privilege was created',
    })
    created_at: Date;

    @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the mst access privilege was last updated',
    })
    updated_at: Date;
}