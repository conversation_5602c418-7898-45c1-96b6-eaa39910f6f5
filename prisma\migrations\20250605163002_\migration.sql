-- AlterTable
ALTER TABLE `car_insurance` ADD COLUMN `quotation_no` INTEGER NULL;

-- AlterTable
ALTER TABLE `car_insurance_customer` ADD COLUMN `car_regis_book_img` VARCHAR(191) NULL,
    ADD COLUMN `id_passport_img` VARCHAR(191) NULL,
    ADD COLUMN `og_insur_policy_img` VARCHAR(191) NULL,
    ADD COLUMN `vehicle_fit_certi` VARCHAR(191) NULL;

-- CreateTable
CREATE TABLE `car_custoemr_image_type` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `car_customer_image` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `car_insurance_customer_id` INTEGER NOT NULL,
    `car_customer_image_type_id` INTEGER NOT NULL,
    `image_path` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `car_customer_image` ADD CONSTRAINT `car_customer_image_car_insurance_customer_id_fkey` FOREIGN KEY (`car_insurance_customer_id`) REFERENCES `car_insurance_customer`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_customer_image` ADD CONSTRAINT `car_customer_image_car_customer_image_type_id_fkey` FOREIGN KEY (`car_customer_image_type_id`) REFERENCES `car_custoemr_image_type`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
