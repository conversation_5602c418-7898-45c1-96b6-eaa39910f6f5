import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  UploadedFile,
  UseInterceptors,
  ParseIntPipe,
  Query,
} from "@nestjs/common";
import { UserRoleService } from "./user-role.service";
import { UserRole } from "./entities/user-role.entity";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";
import { FileInterceptor } from "@nestjs/platform-express";
import { diskStorage } from "multer";
import { CreateUserRoleDto } from "./dto/create-user-role.dto";
import { UpdateUserRoleDto } from "./dto/update-user-role.dto";
import { join } from "path";
import { FileService } from "@common/services/file.service"; // Assuming this is where your utility function is
import { ConvertService } from "@common/services/convert.service";
import { PaginationService } from "@common/services/pagination.service";

@ApiTags("user-role")
@Controller("user-role")
export class UserRoleController {
  constructor(
    private readonly userRoleService: UserRoleService,
    private readonly paginationService: PaginationService,
  ) {}

  @Post()
  @ApiOperation({ summary: "Create a new userRole" })
  @ApiResponse({
    status: 201,
    description: "The userRole has been successfully created.",
    type: CreateUserRoleDto,
  })
  create(@Body() createUserRoleDto: CreateUserRoleDto) { 
    return this.userRoleService.create(createUserRoleDto);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update a userRole by ID" })
  @ApiResponse({
    status: 200,
    description: "The userRole has been successfully updated.",
    type: UpdateUserRoleDto,
  })
  async update(
    @Param("id", ParseIntPipe) id: number,
    @Body() updateUserRoleDto: UpdateUserRoleDto
  ) {
    return this.userRoleService.update(id, updateUserRoleDto);
  }

  @Get()
  @ApiOperation({ summary: "Get a list of all userRoles with pagination" })
  @ApiQuery({ name: "page", required: false, type: Number, example: 1 })
  @ApiQuery({ name: "perPage", required: false, type: Number, example: 10 })
  @ApiQuery({ name: "s", required: false, type: String, example: "camry" })
  @ApiResponse({
    status: 200,
    description: "The list of userRoles has been successfully retrieved.",
    type: [UserRole],
  })
  async pagination(
    @Query("page", ParseIntPipe) page: number = 1,
    @Query("perPage", ParseIntPipe) perPage: number = 10,
    @Query("s") s: string = ""
  ) {
    return this.paginationService.paginate(
      UserRoleService.PATH_FILE,
      page,
      perPage,
      {
        name: {
          contains: s,
        },
      },
      {
        userRoleLog: {
          orderBy: {
            created_at: 'desc'
          },
          take: 1,
          select: {
            created_at: true,
            user_id: true,
            admin: {
              select: {
                first_name: true,
                last_name: true,
              },
            }
          },
        },
      }
    );
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a single userRole by ID" })
  @ApiResponse({
    status: 200,
    description: "The userRole has been successfully retrieved.",
    type: UserRole,
  })
  findOne(@Param("id", ParseIntPipe) id: number) {
    return this.userRoleService.findOne(id);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a userRole by ID" })
  @ApiResponse({
    status: 200,
    description: "The userRole has been successfully deleted.",
    type: UserRole,
  })
  remove(@Param("id", ParseIntPipe) id: number) {
    return this.userRoleService.remove(id);
  }
}
