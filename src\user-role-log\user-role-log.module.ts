import { Modu<PERSON> } from "@nestjs/common";

import { UserRoleLogService } from "./user-role-log.service";
import { PrismaModule } from "src/prisma/prisma.module";
import { FileService } from "@common/services/file.service";
import { PaginationService } from "@common/services/pagination.service";
import { HandleErrorService } from '@common/services/handle-error.services'; 

@Module({
  providers: [UserRoleLogService, FileService, PaginationService,HandleErrorService],
  imports: [PrismaModule],
})
export class UserRoleLogModule {}
