/*
  Warnings:

  - You are about to drop the column `brand_id` on the `car_sub_model` table. All the data in the column will be lost.
  - You are about to drop the column `model_id` on the `car_sub_model` table. All the data in the column will be lost.
  - Added the required column `car_brand_id` to the `car_sub_model` table without a default value. This is not possible if the table is not empty.
  - Added the required column `car_model_id` to the `car_sub_model` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `car_sub_model` DROP FOREIGN KEY `car_sub_model_brand_id_fkey`;

-- DropForeignKey
ALTER TABLE `car_sub_model` DROP FOREIGN KEY `car_sub_model_model_id_fkey`;

-- AlterTable
ALTER TABLE `car_sub_model` DROP COLUMN `brand_id`,
    DROP COLUMN `model_id`,
    ADD COLUMN `car_brand_id` INTEGER NOT NULL,
    ADD COLUMN `car_model_id` INTEGER NOT NULL;

-- AddForeignKey
ALTER TABLE `car_sub_model` ADD CONSTRAINT `car_sub_model_car_brand_id_fkey` FOREIGN KEY (`car_brand_id`) REFERENCES `car_brand`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `car_sub_model` ADD CONSTRAINT `car_sub_model_car_model_id_fkey` FOREIGN KEY (`car_model_id`) REFERENCES `car_model`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
