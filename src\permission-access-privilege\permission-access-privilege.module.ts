import { Modu<PERSON> } from '@nestjs/common';
import { PermissionAccessPrivilegeController } from './permission-access-privilege.controller';
import { PermissionAccessPrivilegeService } from './permission-access-privilege.service';
import { PrismaModule } from 'src/prisma/prisma.module';
import { HandleErrorService } from '@common/services/handle-error.services';

@Module({
  controllers: [PermissionAccessPrivilegeController],
  providers: [PermissionAccessPrivilegeService , HandleErrorService],
  imports: [PrismaModule]
})
export class PermissionAccessPrivilegeModule {}
