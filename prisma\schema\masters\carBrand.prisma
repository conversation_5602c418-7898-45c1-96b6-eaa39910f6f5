model CarBrand {
    id          Int           @id @default(autoincrement())
    code        String        @unique @db.VarChar(50)
    name        String        @db.VarChar(255)
    image_path  String        @db.VarChar(255)
    is_status   Boolean       @default(true)
    is_active   Boolean       @default(true)
    created_at  DateTime      @default(now())
    updated_at  DateTime      @updatedAt
    carModel    CarModel[]
    carSubModel CarSubModel[]
    carInsurance CarInsurance[]
    carActPolicyDetail   CarActPolicyDetail[]
    
    @@map("car_brand")
}
