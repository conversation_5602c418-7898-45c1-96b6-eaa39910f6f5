import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsBoolean, IsInt, IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Parse<PERSON>son } from 'src/_decorators/transform-decorator.decorator';
import { CreatePermissionAccessPriVilegeDto } from 'src/permission-access-privilege/dto/create-permission-access-privilege.dto';

export class CreatePermissionDto {
  @IsString()
  @IsOptional()
  @ApiProperty({ example: 'PER001', description: 'The unique code of the permission' })
  lv_code?: string;

  @IsString()
  @ApiProperty({ example: 'Manage Users', description: 'The name of the permission' })
  name: string;

  @IsBoolean()
  @ApiProperty({ example: true, description: 'The status of the permission', type: Boolean })
  is_status: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ example: true, description: 'Indicates whether the permission is active', type: <PERSON>olean })
  is_active?: boolean;

  @IsInt()
  @IsOptional()
  @ApiProperty({ example: 1, description: 'The unique user_emp_update_id of the permission access privilege', type: Number })
  user_emp_update_id?: number;

  @IsOptional()
  @ValidateNested({ each: true })
  @ParseJson(CreatePermissionAccessPriVilegeDto)
  @Type(() => CreatePermissionAccessPriVilegeDto)
  @ApiProperty({ type: CreatePermissionAccessPriVilegeDto, isArray: true })
  permission_access_items ?: CreatePermissionAccessPriVilegeDto[];
}