import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
// import { CarSubModel } from './entities/car-model.entity';
import { CarSubModel } from '@prisma/client';
import { Prisma } from '@prisma/client';
import { join } from 'path';
import { CreateCarSubModelDto } from './dto/create-car-sub-model.dto';
import { UpdateCarSubModelDto } from './dto/update-car-sub-model.dto';
import { HandleErrorService } from "@common/services/handle-error.services";

@Injectable()
export class CarSubModelService {
  public static MODEL_NAME = 'carSubModel';
  constructor(private prisma: PrismaService,
    private readonly handleErrorService: HandleErrorService) { }

  async create(data: CreateCarSubModelDto): Promise<CarSubModel> {
    try {
      return await this.prisma.carSubModel.create({
        data
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }

  }

  async findAll(filters: { year?: string; car_model_id?: string }): Promise<CarSubModel[]> {
    return await this.prisma.carSubModel.findMany({
      where: {
        is_status: true,
        is_active: true,
        ...(filters.year && { year: parseInt(filters.year, 10) }),
        ...(filters.car_model_id && { car_model_id: parseInt(filters.car_model_id, 10) }),
      },
    });
  }

  async findOne(id: number): Promise<CarSubModel> {
    return this.prisma.carSubModel.findUnique({ where: { id, is_active: true }, include: { carModel: true, carBrand: true }, });
  }

  async update(id: number, data: UpdateCarSubModelDto): Promise<CarSubModel> {
    try {
      return await this.prisma.carSubModel.update({
        where: { id },
        data
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }

  }

  async remove(id: number): Promise<CarSubModel> {
    const existData = await this.prisma.carModel.findUnique({ where: { id, is_active: true } });
    const date = Date.now();
    return this.prisma.carSubModel.update({
      where: { id },
      data: {
        code: date+'-delete-'+existData.code,
        is_active: false,
      },
    });
  }
}
