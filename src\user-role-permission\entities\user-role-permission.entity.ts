import { ApiProperty } from '@nestjs/swagger';

export class UserRolePermission {
  @ApiProperty({
    example: 1,
    description: 'The unique identifier of the user role permission',
  })
  id: number;

  @ApiProperty({
    example: 1,
    description: 'The ID of the associated permission',
    type: Number,
  })
  permission_id: number;

  @ApiProperty({
    example: 1,
    description: 'The ID of the associated user role',
    type: Number,
  })
  user_role_id: number;

  @ApiProperty({
    example: true,
    description: 'Indicates whether the permission is granted',
  })
  is_granted: boolean;

  @ApiProperty({
    example: true,
    description: 'Indicates whether the user role permission is active',
  })
  is_active: boolean;

  @ApiProperty({
    example: true,
    description: 'Indicates the status of the user role permission',
  })
  is_status: boolean;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the user role permission was created',
  })
  created_at: Date;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the user role permission was last updated',
  })
  updated_at: Date;
}