import { HandleErrorService } from "@common/services/handle-error.services";
import { Injectable } from "@nestjs/common";
import { CarInsuranceCustomer } from "@prisma/client";
import { PrismaService } from "src/prisma/prisma.service";

@Injectable()
export class CarInsuranceCustomerService {
  public static MODEL_NAME = "carInsuranceCustomer";
  constructor(
    private prisma: PrismaService,
    private readonly handleErrorService: HandleErrorService
  ) {}

  async findOne(id: number): Promise<CarInsuranceCustomer> {
    return this.prisma.carInsuranceCustomer.findUnique({
      where: { id, is_active: true },
      include: {
        carInsurance: true,
        registrationType: true,
        prefixName: true,
        businessType: true,
        licensePlateProvince: true,
      },
    });
  }
}
