import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsJSON, IsNotEmpty, IsNumber, IsString } from 'class-validator';
import { UserRoleLog } from '../entities/user-role-log.entity';

export class UpdateUserRoleLogDto {
  @IsInt()
  @ApiProperty({ example: 1, description: 'The unique user_id of the UserRoleLog', type: Number })
  user_id: number;

  @IsInt()
  @ApiProperty({ example: 1, description: 'The unique user_id of the UserRoleLog', type: Number })
  user_role_id: number;

  @IsJSON()
  @IsNotEmpty()
  @ApiProperty({ example: 'M0001', description: 'The unique code of the UserRoleLog' })
  data: object;


}