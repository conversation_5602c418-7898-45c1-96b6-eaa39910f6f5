import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsBoolean, IsInt, IsN<PERSON>ber, IsOptional, ValidateNested } from "class-validator";
import { <PERSON><PERSON><PERSON><PERSON> } from "src/_decorators/transform-decorator.decorator";
import { CreateCarInsuranceCompareAddonsDto } from "src/car-insurance-compare-addons/dto/create-car-insurance-compare-addons.dto";

export class CreateCarInsuranceCompareDto {
  @IsInt()
  @IsOptional()
  @Transform(({ value }) => Number(value))
  @ApiProperty({
    example: 1,
    description: "The unique car_insurance_id of the car insurance compare",
    type: Number,
  })
  car_insurance_id?: number;

  @IsInt()
  @Transform(({ value }) => Number(value))
  @ApiProperty({ example: 1, description: "Insurance ID", type: Number })
  insurance_id: number;

  @IsInt()
  @Transform(({ value }) => Number(value))
  @ApiProperty({ example: 1, description: "Insurance Type ID", type: Number })
  insurance_type_id: number;

  @IsInt()
  @IsOptional()
  @Transform(({ value }) => Number(value))
  @ApiProperty({
    example: 1,
    description: "Car Insurance Compare ID",
    type: Number,
  })
  car_insurance_compare_id?: number;

  @IsBoolean()
  @Transform(({ value }) => value === "true" || value === true)
  @IsOptional()
  @ApiProperty({ example: true, description: "Is Named Driver", type: Boolean })
  is_named_driver?: boolean;

  @IsBoolean()
  @Transform(({ value }) => value === "true" || value === true)
  @IsOptional()
  @ApiProperty({ example: true, description: "Is Excess", type: Boolean })
  is_excess?: boolean;

  @IsNumber({}, { each: false })
  @IsOptional()
  @Transform(({ value }) => (value !== "" ? parseFloat(value) : undefined))
  @ApiProperty({ example: 15000.0, description: "Price", type: Number })
  price?: number;

  @IsBoolean()
  @Transform(({ value }) => value === "true" || value === true)
  @IsOptional()
  @ApiProperty({ example: true, description: "Is prb price", type: Boolean })
  is_prb?: boolean;

  @IsNumber({}, { each: false })
  @IsOptional()
  @Transform(({ value }) => (value !== "" ? parseFloat(value) : undefined))
  @ApiProperty({ example: 15000.0, description: "PRB Price", type: Number })
  prb_price?: number;

  @IsBoolean()
  @Transform(({ value }) => value === "true" || value === true)
  @IsOptional()
  @ApiProperty({ example: true, description: "Is Discount", type: Boolean })
  is_discount?: boolean;

  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => (value !== "" ? parseFloat(value) : undefined))
  @ApiProperty({
    example: 500.0,
    description: "discount from agent",
    type: Number,
  })
  agent_discount?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  @ApiProperty({
    example: 100000.0,
    description: "Vehicle Damage",
    type: Number,
  })
  vehicle_damage?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  @ApiProperty({
    example: 200000.0,
    description: "Vehicle Loss or Fire",
    type: Number,
  })
  vehicle_loss_or_fire?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  @ApiProperty({
    example: 500000.0,
    description: "External Person Life per Person",
    type: Number,
  })
  external_person_life_per_person?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  @ApiProperty({
    example: 1000000.0,
    description: "External Person Life per Case",
    type: Number,
  })
  external_person_life_per_case?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  @ApiProperty({
    example: 300000.0,
    description: "External Person Property",
    type: Number,
  })
  external_person_property?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  @ApiProperty({
    example: 50000.0,
    description: "Excess Property Damage",
    type: Number,
  })
  excess_property_damage?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  @ApiProperty({
    example: 100000.0,
    description: "Personal Accident",
    type: Number,
  })
  personal_accident?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  @ApiProperty({
    example: 50000.0,
    description: "Medical Expense Per Person",
    type: Number,
  })
  medical_expense_per_person?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  @ApiProperty({ example: 20000.0, description: "Driver Bail", type: Number })
  driver_bail?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  @ApiProperty({
    example: 500000.0,
    description: "Current Insurance Value",
    type: Number,
  })
  current_insurance_value?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  @ApiProperty({ example: 1500.0, description: "Marketing Cost", type: Number })
  marketing_cost?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  @ApiProperty({ example: 3000.0, description: "Excess", type: Number })
  excess?: number;

  @IsBoolean()
  @Transform(({ value }) => value === "true" || value === true)
  @IsOptional()
  @ApiProperty({ example: true, description: "Is Active", type: Boolean })
  is_active?: boolean;

  @IsOptional()
  @ValidateNested({ each: true })
  @ParseJson(CreateCarInsuranceCompareAddonsDto)
  @Type(() => CreateCarInsuranceCompareAddonsDto)
  @ApiProperty({ type: CreateCarInsuranceCompareAddonsDto, isArray: true })
  car_insurance_compare_addons?: CreateCarInsuranceCompareAddonsDto[];
}
