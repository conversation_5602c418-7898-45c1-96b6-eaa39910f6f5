import { IsOptional, IsBooleanString, IsString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class ItemFilterDto {
    @IsOptional()
    @IsBooleanString()
    @ApiPropertyOptional({ description: 'Filter by status', example: 'true' })
    status?: string;

    @IsOptional()
    @IsString()
    @ApiPropertyOptional({ description: 'Filter by name', example: 'a' })
    name?: string;
}