import { <PERSON>du<PERSON> } from "@nestjs/common";
import { CarSubModelController } from "./car-sub-model.controller";
import { CarSubModelService } from "./car-sub-model.service";
import { PrismaModule } from "src/prisma/prisma.module";
import { FileService } from "@common/services/file.service";
import { PaginationService } from "@common/services/pagination.service";
import { HandleErrorService } from '@common/services/handle-error.services'; 

@Module({
  controllers: [CarSubModelController],
  providers: [CarSubModelService, FileService, PaginationService,HandleErrorService],
  imports: [PrismaModule],
})
export class CarSubModelModule {}
