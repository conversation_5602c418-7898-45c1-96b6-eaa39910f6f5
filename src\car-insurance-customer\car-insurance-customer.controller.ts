import { Body, Controller, Get, Param, ParseIntPipe, Post } from "@nestjs/common";
import { ApiResponse } from "@nestjs/swagger";
import { CarInsuranceCustomerService } from "./car-insurance-customer.service";
import { CreateCarInsuranceCustomerDto } from "./dto/create-car-insurance-customer.dto";

@Controller("car-insurance-customer")
export class CarInsuranceCustomerController {
  constructor(
    private readonly carInsuranceCustomerService: CarInsuranceCustomerService
  ) {}

  @Post()
  @ApiResponse({
    status: 200,
    description: "The car insurance custom has been successfully created.",
    type: CreateCarInsuranceCustomerDto,
  })
  create(@Body() createCarInsuranceCustomerDto: CreateCarInsuranceCustomerDto) {
    console.log("create-CUSTOM", createCarInsuranceCustomerDto);
    // return this.carInsuranceCustomerService.create(createCarInsuranceCustomerDto);
  }

  @Get(":id")
  @ApiResponse({
    status: 200,
    description: "get car insurance customer by id",
  })
  findOne(@Param("id", ParseIntPipe) id: number) {
    return this.carInsuranceCustomerService.findOne(id);
  }
}
