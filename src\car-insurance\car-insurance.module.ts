import { FileService } from "@common/services/file.service";
import { HandleErrorService } from "@common/services/handle-error.services";
import { PaginationService } from "@common/services/pagination.service";
import { Module } from "@nestjs/common";
import { PrismaModule } from "src/prisma/prisma.module";
import { CarInsuranceController } from "./car-insurance.controller";
import { CarInsuranceService } from "./car-insurance.service";

@Module({
  controllers: [CarInsuranceController],
  providers: [
    CarInsuranceService,
    FileService,
    PaginationService,
    HandleErrorService,
  ],
  imports: [PrismaModule],
})
export class CarInsuranceModule {}
