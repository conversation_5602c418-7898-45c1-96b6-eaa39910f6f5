import { IsBoolean, IsString, <PERSON>NotEmpty, IsArray, <PERSON>idateNested ,IsInt} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

import { UpdateUserRolePermissionDto } from "../../user-role-permission/dto/update-user-role-permission.dto";

export class UpdateUserRoleDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    example: "test",
    description: '',
  })
  code: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    example: "test",
    description: '',
  })
  name: string;

  @IsBoolean()
  @ApiProperty({
    example: true,
    description: '',
  })
  is_active: boolean;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateUserRolePermissionDto)
  @ApiProperty({
    type: [UpdateUserRolePermissionDto],
    description: 'List of permissions associated with the user role.',
    example: [
      {
        permission_id: 1,
        is_granted: true,
      },
      {
        permission_id: 2,
        is_granted: true,
      },
    ],
  })
  user_role_permission: UpdateUserRolePermissionDto[];
}