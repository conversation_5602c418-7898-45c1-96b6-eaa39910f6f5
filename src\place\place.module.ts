import { <PERSON>du<PERSON> } from "@nestjs/common";
import { PlaceController } from "./place.controller";
import { PlaceService } from "./place.service";
import { PrismaModule } from "src/prisma/prisma.module";
import { FileService } from "@common/services/file.service";
import { PaginationService } from "@common/services/pagination.service";
import { HandleErrorService } from '@common/services/handle-error.services';

@Module({
  controllers: [PlaceController],
  providers: [PlaceService, FileService, PaginationService, HandleErrorService],
  imports: [PrismaModule],
})
export class PlaceModule { }
