import { PaginationService } from "@common/services/pagination.service";
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from "@nestjs/common";
import {
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { User } from "src/_decorators/user-decorator/user-decorator.decorator";
import { JwtAuthGuard } from "src/auth/jwt-auth.guard";
import { CreatePaymentDto } from "./dto/create-payment.dto";
import { UpdatePaymentDto } from "./dto/update-payment.dto";

import { PaymentService } from "./payment.service";

@ApiTags("payment")
@Controller("payment")
export class PaymentController {
  constructor(
    private readonly paymentService: PaymentService,
    private readonly paginationService: PaginationService
  ) {}

  @Get("paginate")
  @ApiOperation({ summary: "Get a list of all payment with pagination" })
  @ApiQuery({ name: "page", required: false, type: Number, example: 1 })
  @ApiQuery({ name: "perPage", required: false, type: Number, example: 10 })
  @ApiQuery({
    name: "searchText",
    required: false,
    type: String,
    example: "ADMIN",
  })
  @ApiResponse({
    status: 200,
    description: "The list of payment has been successfully retrieved.",
  })
  async pagination(
    @Query("page", ParseIntPipe) page: number = 1,
    @Query("perPage", ParseIntPipe) perPage: number = 10,
    @Query("searchText") searchText: string = ""
  ) {
    return this.paginationService.paginate(
      PaymentService.MODEL_NAME,
      page,
      perPage,
      {
        OR: [
          { name: { contains: searchText.toLowerCase() } },
          { lv_code: { contains: searchText.toLowerCase() } },
        ],
      }
    );
  }

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Create a new payment" })
  @ApiResponse({
    status: 201,
    description: "The payment has been successfully created.",
    type: CreatePaymentDto,
  })
  create(@Body() createPaymentDto: CreatePaymentDto) {
    return this.paymentService.create(createPaymentDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get insurance type model All" })
  @ApiResponse({
    status: 200,
    description: "The insurance type has been successfully retrieved.",
  })
  findAll() {
    return this.paymentService.findAll();
  }

  @Get(":id")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get a single payment by ID" })
  @ApiResponse({
    status: 200,
    description: "The payment has been successfully retrieved.",
  })
  findOne(@Param("id", ParseIntPipe) id: number) {
    return this.paymentService.findOne(id);
  }

  @Put(":id")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Update a payment by ID" })
  @ApiResponse({
    status: 200,
    description: "The payment has been successfully updated.",
    type: UpdatePaymentDto,
  })
  update(
    // @Req() req,
    @Param("id", ParseIntPipe) id: number,
    @Body() updatePaymentDto: UpdatePaymentDto,
    @User() user: any
  ) {
    // console.log('Req',req.user)
    return this.paymentService.update(user.id, id, updatePaymentDto);
  }

  @Delete(":id")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Delete a payment by ID" })
  @ApiResponse({
    status: 200,
    description: "The payment has been successfully deleted.",
  })
  remove(@Param("id", ParseIntPipe) id: number) {
    return this.paymentService.remove(id);
  }
}
