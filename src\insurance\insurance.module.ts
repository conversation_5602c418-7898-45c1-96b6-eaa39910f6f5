import { <PERSON>du<PERSON> } from "@nestjs/common";
import { InsuranceController } from "./insurance.controller";
import { InsuranceService } from "./insurance.service";
import { PrismaModule } from "src/prisma/prisma.module";
import { FileService } from "@common/services/file.service";
import { PaginationService } from "@common/services/pagination.service";
import { HandleErrorService } from '@common/services/handle-error.services';
import { HwcFileUploadService } from "src/hwc-file-upload/hwc-file-upload.service";
import { MulterModule } from "@nestjs/platform-express";

@Module({
  controllers: [InsuranceController],
  providers: [InsuranceService, FileService, PaginationService, HandleErrorService, HwcFileUploadService,],
  imports: [PrismaModule, MulterModule.register()],
})
export class InsuranceModule { }
