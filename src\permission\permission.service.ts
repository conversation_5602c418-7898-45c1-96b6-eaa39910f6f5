import { ConflictException, Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
// import { Permission } from './entities/car-model.entity';
import { Permission } from '@prisma/client';
import { Prisma } from '@prisma/client';
import { join } from 'path';
import { CreatePermissionDto } from './dto/create-permission.dto';
import { UpdatePermissionDto } from './dto/update-permission.dto';
import { HandleErrorService } from "@common/services/handle-error.services";

@Injectable()
export class PermissionService {
  public static MODEL_NAME = 'permission';
  constructor(private prisma: PrismaService,
    private readonly handleErrorService: HandleErrorService) {}

  async create(data: CreatePermissionDto) {
    return this.prisma.$transaction(
      async (prisma: Prisma.TransactionClient) => {
        const {permission_access_items,...createData} = data;
        const newCode = await this.generateCodeNumber();
        try {
          const permissionCreated = await prisma.permission.create({
            data: {
              ...createData,
              lv_code: newCode 
            }
          });
          
          const mappingPermissionItems = permission_access_items.map(item => ({
            ...item,
            permission_id: permissionCreated.id
          }));
          
          await prisma.permissionAccessPrivilege.createMany({
            data: mappingPermissionItems
          });
          
        } catch (error) {
          if (error.code === 'P2002') {
            throw new ConflictException(
              'Unable to generate a unique lv_code after multiple attempts.',
            );
          }
          throw error;
        }
      },
    );
    
  }

  async findAll()/* : Promise<Permission[]>   */{
    return await this.prisma.permission.findMany({
      where: {
        is_status: true,
        is_active: true
      },
    });
  }

  async findOne(id: number)/* : Promise<Permission> */ {
    return this.prisma.permission.findUnique({ 
      where: { id,is_active:true },
      include: { permissionAccessPrivilege: true },
    });
  }

  /* async update(id: number, data: UpdatePermissionDto): Promise<Permission> {
    try {
      return await this.prisma.permission.update({
        where: { id },
        data
      });
    } catch (error) {
      this.handleErrorService.handlePrismaError(error);
    }
    
  } */
  async update(user_id:number,id: number, data: UpdatePermissionDto) {
    return this.prisma.$transaction(
      async (prisma: Prisma.TransactionClient) => {
        let { permission_access_items, ...updateData } = data;
        
        updateData['user_emp_update_id'] = user_id;
        
        try {
          const updatedPermission = await prisma.permission.update({
            where: { id },
            data: updateData,
          });
          if (permission_access_items) {
            await prisma.permissionAccessPrivilege.deleteMany({
              where: { permission_id: id },
            });
  
            const mappingPermissionItems = permission_access_items.map((item) => ({
              ...item,
              permission_id: id,
            }));

            await prisma.permissionAccessPrivilege.createMany({
              data: mappingPermissionItems,
            });
          }
  
          return updatedPermission;
        } catch (error) {
          this.handleErrorService.handlePrismaError(error);
        }
      }
    );
  }

  async remove(id: number): Promise<Permission> {
    const existData = await this.prisma.permission.findUnique({ where: { id, is_active: true } });
    const date = Date.now();
    return this.prisma.permission.update({
      where: { id },
      data: {
        lv_code: date+'-delete-'+existData.lv_code,
        is_active: false,
      },
    });
  }

  async generateCodeNumber() {
    const prefix = 'PER';
  
    const lastRecord = await this.prisma.permission.findFirst({
      where: {
        lv_code: {
          startsWith: prefix,
        },
      },
      orderBy: {
        lv_code: 'desc',
      },
    });
  
    const lastNumber = lastRecord
      ? parseInt(lastRecord.lv_code.replace(prefix, ''), 10)
      : 0;
    const newNumber = String(lastNumber + 1).padStart(3, '0');
  
    return `${prefix}${newNumber}`;
  }
}
