model Admin {
    id             Int      @id @default(autoincrement())
    user_id        Int      @unique
    image_path     String   @db.VarChar(255)
    prefix_name_id Int?
    first_name     String   @db.VarChar(100)
    last_name      String   @db.VarChar(100)
    is_status      Boolean  @default(true)
    is_active      <PERSON>ole<PERSON>  @default(true)
    created_at     DateTime @default(now())
    updated_at     DateTime @updatedAt

    // Relations
    user        User        @relation(fields: [user_id], references: [id], onDelete: Cascade)
    prefix_name PrefixName? @relation(fields: [prefix_name_id], references: [id], onDelete: Cascade)

    userRoleLog        UserRoleLog[]

    @@map("admin")
}
