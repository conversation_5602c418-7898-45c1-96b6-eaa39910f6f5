import { ApiProperty } from '@nestjs/swagger';

export class CarCode {
  @ApiProperty({
    example: 1,
    description: 'The unique identifier of the car code',
  })
  id: number;

  @ApiProperty({
    example: '110',
    description: 'code of the car code',
  })
  code: string;

  @ApiProperty({
    example: 'รถยนต์ส่วนบุคคล-ส่วนบุคคล',
    description: 'The name of the  car code',
  })
  name: string;

  @ApiProperty({
    example: true,
    description: 'Indicates whether the car code is active',
  })
  is_active?: boolean;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the car code was created',
  })
  created_at: Date;

  @ApiProperty({
    example: '2024-08-22T12:34:56Z',
    description: 'The date and time when the car code was last updated',
  })
  updated_at: Date;
}
