import { <PERSON>du<PERSON> } from "@nestjs/common";
import { PermissionController } from "./permission.controller";
import { PermissionService } from "./permission.service";
import { PrismaModule } from "src/prisma/prisma.module";
import { FileService } from "@common/services/file.service";
import { PaginationService } from "@common/services/pagination.service";
import { HandleErrorService } from '@common/services/handle-error.services'; 

@Module({
  controllers: [PermissionController],
  providers: [PermissionService, FileService, PaginationService,HandleErrorService],
  imports: [PrismaModule],
})
export class PermissionModule {}
