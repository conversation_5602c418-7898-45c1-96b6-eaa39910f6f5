import {
  Controller,
  Get,
} from "@nestjs/common";
import { InsuranceAddonServiceService } from "./insurance-addon-service.service";
import { InsuranceAddonService } from "./entities/insurance-addon-service.entity";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
} from "@nestjs/swagger";



@ApiTags("insurance-addon-service")
@Controller("insurance-addon-service")
export class InsuranceAddonServiceController {
  constructor(
    private readonly insuranceAddonServiceService: InsuranceAddonServiceService,
  ) { }

  @Get("")
  @ApiOperation({ summary: "Get All InsuranceAddonService" })
  @ApiResponse({
    status: 200,
    description: "The insurance-addon-service has been successfully retrieved.",
    type: InsuranceAddonService,
  })
  findAll() {
    return this.insuranceAddonServiceService.findAll();
  }
}
