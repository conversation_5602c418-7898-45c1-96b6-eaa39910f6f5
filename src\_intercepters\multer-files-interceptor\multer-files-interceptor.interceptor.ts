import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Observable, catchError } from 'rxjs';
import { unlink } from 'fs';
import { Request } from 'express';

@Injectable()
export class FilesInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    
    return next.handle().pipe(
      catchError((error) => {

        const files = (request as Request).files as { [fieldname: string]: Express.Multer.File[] };
        if (files) {
          Object.values(files).flat().forEach((file) => {
            unlink(file.path, (err) => {
              if (err) console.error(`Failed to delete file: ${file.path}`);
              else console.log(`Deleted file: ${file.path}`);
            });
          });
        }
        
        throw error;
      }),
    );
  }
}
