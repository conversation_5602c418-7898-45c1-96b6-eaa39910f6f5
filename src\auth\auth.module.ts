import { HandleErrorService } from "@common/services/handle-error.services";
import { Module } from "@nestjs/common";
import { JwtModule } from "@nestjs/jwt";
import { PassportModule } from "@nestjs/passport";
import { jwtSecret } from "src/constants/jwtSecret";
import { PrismaModule } from "src/prisma/prisma.module";

import { AdminService } from "src/admin/admin.service";
import { UserService } from "src/user/user.service";
import { AuthController } from "./auth.controller";
import { AuthService } from "./auth.service";
import { JwtStrategy } from "./jwt.strategy";

@Module({
  imports: [
    PrismaModule,
    PassportModule,
    JwtModule.register({
      secret: jwtSecret,
      signOptions: { expiresIn: "730d" }, // e.g. 30s, 7d, 24h
    }),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    JwtStrategy,
    HandleErrorService,
    UserService,
    AdminService,
  ],
})
export class AuthModule {}
