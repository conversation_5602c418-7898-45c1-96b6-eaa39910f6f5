import { <PERSON>du<PERSON> } from "@nestjs/common";
import { UserRoleController } from "./user-role.controller";
import { UserRoleService } from "./user-role.service";
import { PrismaModule } from "src/prisma/prisma.module";
import { FileService } from "@common/services/file.service";
import { PaginationService } from "@common/services/pagination.service";
import { HandleErrorService } from '@common/services/handle-error.services'; 
import { UserRolePermissionService } from "../user-role-permission/user-role-permission.service";
import { UserRoleLogService } from "../user-role-log/user-role-log.service";
@Module({
  controllers: [UserRoleController],
  providers: [UserRoleService, FileService, PaginationService,HandleErrorService,UserRolePermissionService,UserRoleLogService],
  imports: [PrismaModule],
})
export class UserRoleModule {}
