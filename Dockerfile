# Base image
FROM node:20-alpine3.19 as build

# Create app directory
WORKDIR /usr/src/app

# A wildcard is used to ensure both package.json AND package-lock.json are copied
COPY package*.json ./
COPY prisma ./prisma

# Install app dependencies
# Consider separating production dependencies if applicable
RUN yarn install

# If you are using TypeScript or need to transpile your code, uncomment the next line
# RUN npm run build

# Bundle app source
COPY . .

# Copy Media to Dist
# Make sure the media directory exists in your Docker context
# COPY media ./dist/media

RUN npx prisma migrate deploy
# Generates the Prisma Client
RUN npx prisma generate

# yarn only first time
RUN yarn seed
# Build your app
RUN yarn build

EXPOSE 3000

# Choose the appropriate command to start your application
# For development, you might use:
# CMD [ "npm", "run", "start:dev" ]

# For production, assuming your build outputs to the dist folder:
CMD [ "node", "dist/main.js" ]

