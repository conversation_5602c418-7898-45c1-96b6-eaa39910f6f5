import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEmail, IsInt, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class UpdateUserAdminDto {
  // User properties
  @IsString()
  @ApiProperty({ example: 'john_doe', description: 'The unique username of the user' , required: false})
  username: string;

  @IsString()
  @ApiProperty({ example: 'securePassword123', description: 'The password for the user account', required: false })
  password: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ example: '+**********', description: 'The phone number of the user', required: false })
  phone_number?: string;

  @IsEmail()
  @ApiProperty({ example: '<EMAIL>', description: 'The email address of the user', required: false })
  email: string;

  @IsBoolean()
  @ApiProperty({ example: true, description: 'Indicates whether the user is active', type: Boolean , required: false})
  is_active: boolean;

  @IsInt()
  @ApiProperty({ example: 1, description: 'The unique user_role_id of the user', type: Number, required: false })
  user_role_id: number;

  @IsInt()
  @ApiProperty({ example: 1, description: 'The unique user_type_id of the user', type: Number , required: false})
  user_type_id: number;

 

  @IsOptional()
  @IsInt()
  @ApiProperty({ example: 3, description: 'The parent user ID', required: false })
  parent_id?: number;

  // Admin properties
  @IsString()
  @ApiProperty({ type: 'string', format: 'binary', description: 'Image file for the car brand' ,required: false })
  image_path: any;

  @IsOptional()
  @IsInt()
  @ApiProperty({ example: 1, description: 'The prefix name ID for the admin', required: false })
  prefix_name_id?: number;

  @IsString()
  @ApiProperty({ example: 'John', description: 'The first name of the admin', required: false })
  first_name: string;

  @IsString()
  @ApiProperty({ example: 'Doe', description: 'The last name of the admin', required: false })
  last_name: string;
 
}