import { PartialType } from '@nestjs/swagger';
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';
import { CarSubModel } from '../entities/car-sub-model.entity';

export class UpdateCarSubModelDto {
  @IsInt()
  @ApiProperty({ example: 1, description: 'The unique car_brand_id of the car sub model', type: Number })
  car_brand_id: number;

  @IsInt()
  @ApiProperty({ example: 1, description: 'The unique car_model_id of the car sub model', type: Number })
  car_model_id: number;

  @IsString()
  @ApiProperty({ example: 'M0001', description: 'The unique code of the car sub model', required: false })
  code?: string;

  @IsString()
  @ApiProperty({ example: 'Camry', description: 'The name of the car sub model', required: false })
  name?: string;

  @IsInt()
  @ApiProperty({ example: '2019', description: 'The year of the car sub model' })
  year: number;

  @IsBoolean()
  @ApiProperty({ example: true, description: 'The status of the car sub model', type: Boolean, required: false })
  is_status?: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ example: true, description: 'Indicates whether the car sub model is active', type: Boolean, required: false })
  is_active?: boolean;
}