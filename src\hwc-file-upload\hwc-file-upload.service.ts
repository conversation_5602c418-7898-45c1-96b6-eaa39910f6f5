import { Injectable } from '@nestjs/common';
import axios from 'axios';
import * as fs from 'fs';
import FormData from 'form-data';
import { HWC_UPLOAD_URL } from 'src/configs/server';
import { HwcUploadType } from 'src/types/hwc-file-upload.type';

@Injectable()
export class HwcFileUploadService {
  async uploadFile(payload: HwcUploadType) {
    const form = new FormData();
    
    form.append('success_action_status', payload.success_action_status || '200');
    form.append('key', payload.key);
    form.append('file', payload.file.buffer, {
      filename: payload.file.originalname,
      contentType: payload.file.mimetype,
    });

    try {
      const response = await axios.post(HWC_UPLOAD_URL, form, {
        headers: {
          ...form.getHeaders(),
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(`File upload failed: ${error.message}`);
    }
  }
}
